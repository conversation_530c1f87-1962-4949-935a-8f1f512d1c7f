<template>
  <div class="usb-printer">
    <div class="printer-controls">
      <h2>USB打印机控制</h2>
      
      <!-- 打印方式选择 -->
      <div class="print-method-section">
        <h3>选择打印方式</h3>
        <div class="method-options">
          <label class="method-option">
            <input type="radio" v-model="printMethod" value="browser" />
            <span class="method-info">
              <strong>浏览器打印 (推荐)</strong>
              <small>使用系统默认打印机，兼容性最好</small>
            </span>
          </label>
          <label class="method-option">
            <input type="radio" v-model="printMethod" value="serial" />
            <span class="method-info">
              <strong>串口直连</strong>
              <small>适用于支持串口的热敏打印机</small>
            </span>
          </label>
          <label class="method-option">
            <input type="radio" v-model="printMethod" value="pdf" />
            <span class="method-info">
              <strong>PDF导出</strong>
              <small>保存为PDF文件，可稍后打印</small>
            </span>
          </label>
        </div>
      </div>

      <!-- 打印机连接 -->
      <div class="connection-section" v-if="printMethod === 'serial'">
        <h3>串口打印机连接</h3>
        <button
          @click="connectSerialPrinter"
          :disabled="isConnected || isConnecting"
          class="connect-btn"
        >
          {{ isConnecting ? '连接中...' : (isConnected ? '已连接' : '连接串口打印机') }}
        </button>
        <button
          @click="disconnectPrinter"
          :disabled="!isConnected"
          class="disconnect-btn"
          v-if="isConnected"
        >
          断开连接
        </button>
        <span v-if="isConnected" class="status connected">✓ 串口打印机已连接</span>
        <span v-else-if="isConnecting" class="status connecting">⟳ 连接中...</span>
        <span v-else class="status disconnected">○ 未连接</span>

        <div class="connection-help">
          <p><strong>连接提示：</strong></p>
          <ul>
            <li>确保打印机支持串口通信（多数USB打印机不支持）</li>
            <li>如果找不到设备，建议使用"浏览器打印"方式</li>
            <li>支持的浏览器：Chrome 89+, Edge 89+</li>
          </ul>
        </div>
      </div>

      <!-- 打印机检测 -->
      <div class="detection-section" v-if="printMethod === 'browser'">
        <h3>系统打印机</h3>
        <button
          @click="detectPrinters"
          :disabled="isConnecting"
          class="detect-btn"
        >
          {{ isConnecting ? '检测中...' : '检测可用打印机' }}
        </button>

        <div v-if="showPrinterList && availablePrinters.length > 0" class="printer-list">
          <h4>可用打印机：</h4>
          <div class="printer-items">
            <label v-for="printer in availablePrinters" :key="printer.id" class="printer-item">
              <input
                type="radio"
                :value="printer"
                v-model="selectedPrinter"
                @change="printerDetectionService.setDefaultPrinter(printer.id)"
              />
              <span class="printer-info">
                <strong>{{ printer.name }}</strong>
                <small>{{ printer.type }} - {{ printer.status }}</small>
                <span v-if="printer.isDefault" class="default-badge">默认</span>
              </span>
            </label>
          </div>
        </div>
      </div>

      <!-- 打印内容输入 -->
      <div class="content-section">
        <h3>打印内容</h3>
        <textarea 
          v-model="printContent" 
          placeholder="请输入要打印的内容..."
          rows="6"
          class="print-textarea"
        ></textarea>
      </div>

      <!-- 打印设置 -->
      <div class="settings-section">
        <h3>打印设置</h3>
        <div class="setting-item">
          <label>纸张大小:</label>
          <select v-model="paperSize">
            <option value="A4">A4</option>
            <option value="A5">A5</option>
            <option value="Letter">Letter</option>
            <option value="58mm">58mm热敏纸</option>
            <option value="80mm">80mm热敏纸</option>
          </select>
        </div>
        <div class="setting-item">
          <label>字体大小:</label>
          <select v-model="fontSize">
            <option value="12">12px</option>
            <option value="14">14px</option>
            <option value="16">16px</option>
            <option value="18">18px</option>
            <option value="20">20px</option>
          </select>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" v-model="autoCut"> 自动切纸
          </label>
        </div>
      </div>

      <!-- 打印按钮 -->
      <div class="print-section">
        <button
          @click="executePrint"
          :disabled="!printContent.trim() || isPrinting || (printMethod === 'serial' && !isConnected)"
          class="print-btn primary"
        >
          {{ isPrinting ? '打印中...' : getPrintButtonText() }}
        </button>
        <button
          @click="printHTML"
          :disabled="!printContent.trim()"
          class="print-btn secondary"
          v-if="printMethod === 'browser'"
        >
          打印HTML内容 (带预览)
        </button>
        <button
          @click="printTestPage"
          :disabled="isPrinting || (printMethod === 'serial' && !isConnected)"
          class="print-btn test"
          v-if="printMethod === 'serial'"
        >
          {{ isPrinting ? '打印中...' : '打印测试页' }}
        </button>
      </div>

      <!-- 预设模板 -->
      <div class="template-section">
        <h3>快速模板</h3>
        <div class="template-buttons">
          <button @click="loadReceiptTemplate" class="template-btn">收据模板</button>
          <button @click="loadLabelTemplate" class="template-btn">标签模板</button>
          <button @click="loadTestTemplate" class="template-btn">测试页面</button>
        </div>
      </div>
    </div>

    <!-- 打印预览区域 (隐藏) -->
    <div id="print-area" style="display: none;">
      <div class="print-content" :style="printStyles">
        <pre>{{ printContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { printerService, type PrintJob } from '../services/PrinterService'
import { printerDetectionService, type PrinterInfo, type PrintOptions } from '../services/PrinterDetectionService'

// 响应式数据
const isConnected = ref(false)
const printContent = ref('')
const paperSize = ref('A4')
const fontSize = ref('14')
const autoCut = ref(true)
const isConnecting = ref(false)
const isPrinting = ref(false)
const availablePrinters = ref<PrinterInfo[]>([])
const selectedPrinter = ref<PrinterInfo | null>(null)
const printMethod = ref<'browser' | 'serial' | 'pdf'>('browser')
const showPrinterList = ref(false)

// 计算属性
const printStyles = computed(() => ({
  fontSize: fontSize.value + 'px',
  fontFamily: 'monospace',
  lineHeight: '1.5',
  padding: '20px',
  width: paperSize.value === '58mm' ? '58mm' :
         paperSize.value === '80mm' ? '80mm' : '210mm'
}))

// 检测可用打印机
const detectPrinters = async () => {
  try {
    isConnecting.value = true
    const printers = await printerDetectionService.detectPrinters()
    availablePrinters.value = printers
    selectedPrinter.value = printerDetectionService.getDefaultPrinter()
    showPrinterList.value = true

    if (printers.length === 0) {
      alert('未检测到可用的打印机')
    }
  } catch (error) {
    console.error('检测打印机失败:', error)
    alert('检测打印机失败: ' + (error as Error).message)
  } finally {
    isConnecting.value = false
  }
}

// 连接串口打印机
const connectSerialPrinter = async () => {
  if (isConnecting.value) return

  try {
    isConnecting.value = true

    if (!printerService.constructor.isSupported()) {
      alert('您的浏览器不支持Web Serial API，请使用Chrome 89+或Edge 89+\n\n建议使用"浏览器打印"方式作为替代方案。')
      return
    }

    // 尝试请求新的串口设备
    const printer = await printerDetectionService.requestSerialPrinter()
    if (printer) {
      selectedPrinter.value = printer
      await printerService.connect()

      // 更新打印机配置
      printerService.updateConfig({
        paperWidth: paperSize.value === '58mm' ? 58 : paperSize.value === '80mm' ? 80 : 210,
        fontSize: parseInt(fontSize.value),
        autoCut: autoCut.value
      })

      isConnected.value = true
      printMethod.value = 'serial'
      console.log('串口打印机连接成功')
    }

  } catch (error) {
    console.error('连接串口打印机失败:', error)
    let errorMessage = '连接失败: ' + (error as Error).message

    if ((error as Error).message.includes('No port selected')) {
      errorMessage = '未选择设备或设备不兼容。\n\n大多数USB打印机不支持串口通信，建议使用"浏览器打印"方式。'
    }

    alert(errorMessage)
    isConnected.value = false
  } finally {
    isConnecting.value = false
  }
}

// 执行打印 (根据选择的方式)
const executePrint = async () => {
  if (!printContent.value.trim()) {
    alert('请输入要打印的内容')
    return
  }

  if (isPrinting.value) return

  try {
    isPrinting.value = true

    switch (printMethod.value) {
      case 'browser':
        await printWithBrowser()
        break
      case 'serial':
        await printWithSerial()
        break
      case 'pdf':
        await exportToPDF()
        break
      default:
        await printWithBrowser()
    }

    console.log('打印完成')
    alert('打印完成!')

  } catch (error) {
    console.error('打印失败:', error)
    alert('打印失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 浏览器打印
const printWithBrowser = async () => {
  const options: PrintOptions = {
    paperSize: paperSize.value,
    copies: 1,
    silent: false
  }

  await printerDetectionService.printWithBrowser(printContent.value, options)
}

// 串口打印
const printWithSerial = async () => {
  if (!isConnected.value) {
    throw new Error('串口打印机未连接')
  }

  // 更新配置
  printerService.updateConfig({
    paperWidth: paperSize.value === '58mm' ? 58 : paperSize.value === '80mm' ? 80 : 210,
    fontSize: parseInt(fontSize.value),
    autoCut: autoCut.value
  })

  // 创建打印任务
  const printJob: PrintJob = {
    content: printContent.value,
    type: 'text'
  }

  // 执行打印
  await printerService.print(printJob)
}

// 导出PDF
const exportToPDF = async () => {
  const filename = `print-${new Date().toISOString().slice(0, 10)}.pdf`
  await printerDetectionService.exportToPDF(printContent.value, filename)
}

// 断开连接
const disconnectPrinter = async () => {
  try {
    await printerService.disconnect()
    isConnected.value = false
    console.log('打印机已断开连接')
  } catch (error) {
    console.error('断开连接失败:', error)
  }
}

// 打印测试页
const printTestPage = async () => {
  if (!isConnected.value) {
    alert('请先连接打印机')
    return
  }

  try {
    isPrinting.value = true
    await printerService.printTestPage()
    alert('测试页打印完成!')
  } catch (error) {
    console.error('打印测试页失败:', error)
    alert('打印测试页失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 使用浏览器打印API打印HTML内容
const printHTML = () => {
  const printArea = document.getElementById('print-area')
  if (!printArea) return
  
  // 显示打印区域
  printArea.style.display = 'block'
  
  // 创建新窗口进行打印
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    alert('无法打开打印窗口，请检查浏览器弹窗设置')
    return
  }
  
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>打印内容</title>
      <style>
        body { 
          font-family: monospace; 
          font-size: ${fontSize.value}px; 
          line-height: 1.5; 
          margin: 0; 
          padding: 20px; 
        }
        @media print {
          body { margin: 0; }
          @page { margin: 0.5in; }
        }
      </style>
    </head>
    <body>
      <pre>${printContent.value}</pre>
    </body>
    </html>
  `)
  
  printWindow.document.close()
  
  // 等待内容加载后打印
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
    printArea.style.display = 'none'
  }, 500)
}

// 加载收据模板
const loadReceiptTemplate = () => {
  printContent.value = `================================
           收据
================================
日期: ${new Date().toLocaleDateString()}
时间: ${new Date().toLocaleTimeString()}

商品名称          数量    单价    金额
--------------------------------
苹果              2kg    ¥6.00   ¥12.00
香蕉              1kg    ¥4.50   ¥4.50
橙子              3kg    ¥5.00   ¥15.00
--------------------------------
合计:                           ¥31.50
实收:                           ¥35.00
找零:                           ¥3.50

谢谢惠顾！
================================`
}

// 加载标签模板
const loadLabelTemplate = () => {
  printContent.value = `┌─────────────────────────────┐
│        产品标签             │
├─────────────────────────────┤
│ 产品名称: 优质苹果          │
│ 产地: 山东烟台              │
│ 规格: 5kg/箱                │
│ 生产日期: ${new Date().toLocaleDateString()}        │
│ 保质期: 30天                │
│ 价格: ¥30.00/箱             │
└─────────────────────────────┘`
}

// 加载测试模板
const loadTestTemplate = () => {
  printContent.value = `打印机测试页面

字体测试:
ABCDEFGHIJKLMNOPQRSTUVWXYZ
abcdefghijklmnopqrstuvwxyz
0123456789

中文测试:
你好世界！这是一个打印测试。
测试各种字符：！@#￥%……&*（）

线条测试:
================================
--------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
********************************

打印时间: ${new Date().toLocaleString()}
测试完成！`
}

// 获取打印按钮文本
const getPrintButtonText = (): string => {
  switch (printMethod.value) {
    case 'browser':
      return '浏览器打印 (无预览)'
    case 'serial':
      return '串口直接打印'
    case 'pdf':
      return '导出为PDF'
    default:
      return '开始打印'
  }
}

// 组件挂载时初始化
onMounted(async () => {
  if (!printerService.constructor.isSupported()) {
    console.warn('当前浏览器不支持Web Serial API，将使用浏览器打印作为默认方式')
    printMethod.value = 'browser'
  }

  // 检查是否已经连接串口
  isConnected.value = printerService.getConnectionStatus()

  // 如果默认是浏览器打印，自动检测打印机
  if (printMethod.value === 'browser') {
    await detectPrinters()
  }
})

// 组件卸载时断开连接
onUnmounted(async () => {
  if (isConnected.value) {
    await disconnectPrinter()
  }
})
</script>

<style scoped>
.usb-printer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.printer-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.printer-controls h2 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.connection-section {
  margin-bottom: 20px;
  text-align: center;
}

.connect-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
}

.connect-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.disconnect-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-left: 10px;
}

.disconnect-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.status {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 4px;
}

.status.connected {
  color: #28a745;
  background: #d4edda;
}

.status.disconnected {
  color: #dc3545;
  background: #f8d7da;
}

.status.connecting {
  color: #ffc107;
  background: #fff3cd;
}

.print-method-section, .connection-section, .detection-section,
.content-section, .settings-section, .print-section, .template-section {
  margin-bottom: 20px;
}

.print-method-section h3, .connection-section h3, .detection-section h3,
.content-section h3, .settings-section h3, .template-section h3 {
  margin-bottom: 10px;
  color: #555;
}

/* 打印方式选择样式 */
.method-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.method-option {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.method-option:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.method-option input[type="radio"]:checked + .method-info {
  color: #007bff;
}

.method-option input[type="radio"] {
  margin-top: 2px;
}

.method-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.method-info strong {
  font-size: 14px;
}

.method-info small {
  color: #6c757d;
  font-size: 12px;
}

/* 连接帮助样式 */
.connection-help {
  margin-top: 15px;
  padding: 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 13px;
}

.connection-help p {
  margin: 0 0 8px 0;
  font-weight: bold;
  color: #856404;
}

.connection-help ul {
  margin: 0;
  padding-left: 20px;
  color: #856404;
}

.connection-help li {
  margin-bottom: 4px;
}

.print-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  resize: vertical;
}

.setting-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.setting-item label {
  min-width: 80px;
  font-weight: bold;
}

.setting-item select {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.print-section {
  text-align: center;
}

.print-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin: 0 10px;
}

.print-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.print-btn.secondary {
  background: #17a2b8;
}

.print-btn.test {
  background: #6f42c1;
}

.print-btn.primary {
  background: #28a745;
  font-size: 18px;
  padding: 14px 28px;
}

/* 检测按钮样式 */
.detect-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 15px;
}

.detect-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* 打印机列表样式 */
.printer-list {
  margin-top: 15px;
}

.printer-list h4 {
  margin-bottom: 10px;
  color: #495057;
  font-size: 14px;
}

.printer-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.printer-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.printer-item:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.printer-item input[type="radio"]:checked + .printer-info {
  color: #007bff;
}

.printer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.printer-info strong {
  font-size: 14px;
}

.printer-info small {
  color: #6c757d;
  font-size: 12px;
}

.default-badge {
  display: inline-block;
  background: #28a745;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.template-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.template-btn {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.template-btn:hover {
  background: #e0a800;
}

.print-content {
  background: white;
  border: 1px solid #ddd;
  padding: 20px;
  white-space: pre-wrap;
}
</style>
