<template>
  <div class="usb-printer">
    <div class="printer-controls">
      <h2>USB打印机控制</h2>
      
      <!-- 连接打印机 -->
      <div class="connection-section">
        <button
          @click="connectPrinter"
          :disabled="isConnected || isConnecting"
          class="connect-btn"
        >
          {{ isConnecting ? '连接中...' : (isConnected ? '已连接' : '连接USB打印机') }}
        </button>
        <button
          @click="disconnectPrinter"
          :disabled="!isConnected"
          class="disconnect-btn"
          v-if="isConnected"
        >
          断开连接
        </button>
        <span v-if="isConnected" class="status connected">✓ 打印机已连接</span>
        <span v-else-if="isConnecting" class="status connecting">⟳ 连接中...</span>
        <span v-else class="status disconnected">○ 未连接</span>
      </div>

      <!-- 打印内容输入 -->
      <div class="content-section">
        <h3>打印内容</h3>
        <textarea 
          v-model="printContent" 
          placeholder="请输入要打印的内容..."
          rows="6"
          class="print-textarea"
        ></textarea>
      </div>

      <!-- 打印设置 -->
      <div class="settings-section">
        <h3>打印设置</h3>
        <div class="setting-item">
          <label>纸张大小:</label>
          <select v-model="paperSize">
            <option value="A4">A4</option>
            <option value="A5">A5</option>
            <option value="Letter">Letter</option>
            <option value="58mm">58mm热敏纸</option>
            <option value="80mm">80mm热敏纸</option>
          </select>
        </div>
        <div class="setting-item">
          <label>字体大小:</label>
          <select v-model="fontSize">
            <option value="12">12px</option>
            <option value="14">14px</option>
            <option value="16">16px</option>
            <option value="18">18px</option>
            <option value="20">20px</option>
          </select>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" v-model="autoCut"> 自动切纸
          </label>
        </div>
      </div>

      <!-- 打印按钮 -->
      <div class="print-section">
        <button
          @click="directPrint"
          :disabled="!isConnected || !printContent.trim() || isPrinting"
          class="print-btn"
        >
          {{ isPrinting ? '打印中...' : '直接打印 (无预览)' }}
        </button>
        <button
          @click="printHTML"
          :disabled="!printContent.trim()"
          class="print-btn secondary"
        >
          打印HTML内容
        </button>
        <button
          @click="printTestPage"
          :disabled="!isConnected || isPrinting"
          class="print-btn test"
        >
          打印测试页
        </button>
      </div>

      <!-- 预设模板 -->
      <div class="template-section">
        <h3>快速模板</h3>
        <div class="template-buttons">
          <button @click="loadReceiptTemplate" class="template-btn">收据模板</button>
          <button @click="loadLabelTemplate" class="template-btn">标签模板</button>
          <button @click="loadTestTemplate" class="template-btn">测试页面</button>
        </div>
      </div>
    </div>

    <!-- 打印预览区域 (隐藏) -->
    <div id="print-area" style="display: none;">
      <div class="print-content" :style="printStyles">
        <pre>{{ printContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { printerService, type PrintJob } from '../services/PrinterService'

// 响应式数据
const isConnected = ref(false)
const printContent = ref('')
const paperSize = ref('80mm')
const fontSize = ref('14')
const autoCut = ref(true)
const isConnecting = ref(false)
const isPrinting = ref(false)

// 计算属性
const printStyles = computed(() => ({
  fontSize: fontSize.value + 'px',
  fontFamily: 'monospace',
  lineHeight: '1.5',
  padding: '20px',
  width: paperSize.value === '58mm' ? '58mm' :
         paperSize.value === '80mm' ? '80mm' : '210mm'
}))

// 连接USB打印机
const connectPrinter = async () => {
  if (isConnecting.value) return

  try {
    isConnecting.value = true

    if (!printerService.constructor.isSupported()) {
      alert('您的浏览器不支持Web Serial API，请使用Chrome 89+或Edge 89+')
      return
    }

    await printerService.connect()

    // 更新打印机配置
    printerService.updateConfig({
      paperWidth: paperSize.value === '58mm' ? 58 : 80,
      fontSize: parseInt(fontSize.value),
      autoCut: autoCut.value
    })

    isConnected.value = true
    console.log('USB打印机连接成功')

  } catch (error) {
    console.error('连接打印机失败:', error)
    alert('连接打印机失败: ' + (error as Error).message)
    isConnected.value = false
  } finally {
    isConnecting.value = false
  }
}

// 直接打印 (使用打印服务)
const directPrint = async () => {
  if (!isConnected.value) {
    alert('请先连接打印机')
    return
  }

  if (isPrinting.value) return

  try {
    isPrinting.value = true

    // 更新配置
    printerService.updateConfig({
      paperWidth: paperSize.value === '58mm' ? 58 : 80,
      fontSize: parseInt(fontSize.value),
      autoCut: autoCut.value
    })

    // 创建打印任务
    const printJob: PrintJob = {
      content: printContent.value,
      type: 'text'
    }

    // 执行打印
    await printerService.print(printJob)

    console.log('打印完成')
    alert('打印完成!')

  } catch (error) {
    console.error('打印失败:', error)
    alert('打印失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 断开连接
const disconnectPrinter = async () => {
  try {
    await printerService.disconnect()
    isConnected.value = false
    console.log('打印机已断开连接')
  } catch (error) {
    console.error('断开连接失败:', error)
  }
}

// 打印测试页
const printTestPage = async () => {
  if (!isConnected.value) {
    alert('请先连接打印机')
    return
  }

  try {
    isPrinting.value = true
    await printerService.printTestPage()
    alert('测试页打印完成!')
  } catch (error) {
    console.error('打印测试页失败:', error)
    alert('打印测试页失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 使用浏览器打印API打印HTML内容
const printHTML = () => {
  const printArea = document.getElementById('print-area')
  if (!printArea) return
  
  // 显示打印区域
  printArea.style.display = 'block'
  
  // 创建新窗口进行打印
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    alert('无法打开打印窗口，请检查浏览器弹窗设置')
    return
  }
  
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>打印内容</title>
      <style>
        body { 
          font-family: monospace; 
          font-size: ${fontSize.value}px; 
          line-height: 1.5; 
          margin: 0; 
          padding: 20px; 
        }
        @media print {
          body { margin: 0; }
          @page { margin: 0.5in; }
        }
      </style>
    </head>
    <body>
      <pre>${printContent.value}</pre>
    </body>
    </html>
  `)
  
  printWindow.document.close()
  
  // 等待内容加载后打印
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
    printArea.style.display = 'none'
  }, 500)
}

// 加载收据模板
const loadReceiptTemplate = () => {
  printContent.value = `================================
           收据
================================
日期: ${new Date().toLocaleDateString()}
时间: ${new Date().toLocaleTimeString()}

商品名称          数量    单价    金额
--------------------------------
苹果              2kg    ¥6.00   ¥12.00
香蕉              1kg    ¥4.50   ¥4.50
橙子              3kg    ¥5.00   ¥15.00
--------------------------------
合计:                           ¥31.50
实收:                           ¥35.00
找零:                           ¥3.50

谢谢惠顾！
================================`
}

// 加载标签模板
const loadLabelTemplate = () => {
  printContent.value = `┌─────────────────────────────┐
│        产品标签             │
├─────────────────────────────┤
│ 产品名称: 优质苹果          │
│ 产地: 山东烟台              │
│ 规格: 5kg/箱                │
│ 生产日期: ${new Date().toLocaleDateString()}        │
│ 保质期: 30天                │
│ 价格: ¥30.00/箱             │
└─────────────────────────────┘`
}

// 加载测试模板
const loadTestTemplate = () => {
  printContent.value = `打印机测试页面

字体测试:
ABCDEFGHIJKLMNOPQRSTUVWXYZ
abcdefghijklmnopqrstuvwxyz
0123456789

中文测试:
你好世界！这是一个打印测试。
测试各种字符：！@#￥%……&*（）

线条测试:
================================
--------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
********************************

打印时间: ${new Date().toLocaleString()}
测试完成！`
}

// 组件挂载时检查浏览器支持
onMounted(() => {
  if (!printerService.constructor.isSupported()) {
    console.warn('当前浏览器不支持Web Serial API')
  }

  // 检查是否已经连接
  isConnected.value = printerService.getConnectionStatus()
})

// 组件卸载时断开连接
onUnmounted(async () => {
  if (isConnected.value) {
    await disconnectPrinter()
  }
})
</script>

<style scoped>
.usb-printer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.printer-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.printer-controls h2 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.connection-section {
  margin-bottom: 20px;
  text-align: center;
}

.connect-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
}

.connect-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.disconnect-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-left: 10px;
}

.disconnect-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.status {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 4px;
}

.status.connected {
  color: #28a745;
  background: #d4edda;
}

.status.disconnected {
  color: #dc3545;
  background: #f8d7da;
}

.status.connecting {
  color: #ffc107;
  background: #fff3cd;
}

.content-section, .settings-section, .print-section, .template-section {
  margin-bottom: 20px;
}

.content-section h3, .settings-section h3, .template-section h3 {
  margin-bottom: 10px;
  color: #555;
}

.print-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  resize: vertical;
}

.setting-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.setting-item label {
  min-width: 80px;
  font-weight: bold;
}

.setting-item select {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.print-section {
  text-align: center;
}

.print-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin: 0 10px;
}

.print-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.print-btn.secondary {
  background: #17a2b8;
}

.print-btn.test {
  background: #6f42c1;
}

.template-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.template-btn {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.template-btn:hover {
  background: #e0a800;
}

.print-content {
  background: white;
  border: 1px solid #ddd;
  padding: 20px;
  white-space: pre-wrap;
}
</style>
