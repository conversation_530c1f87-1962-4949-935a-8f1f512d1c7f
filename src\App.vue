<script setup lang="ts">
import USBPrinter from './components/USBPrinter.vue'
import PrintingGuide from './components/PrintingGuide.vue'
</script>

<template>
  <div id="app">
    <header class="app-header">
      <h1>Vue3 多方式打印 Demo</h1>
      <p>支持浏览器打印、串口直连、PDF导出等多种打印方式</p>
      <div class="feature-badges">
        <span class="badge">✓ 无预览打印</span>
        <span class="badge">✓ 多种打印方式</span>
        <span class="badge">✓ 兼容性强</span>
      </div>
    </header>

    <main class="app-main">
      <PrintingGuide />
      <USBPrinter />
    </main>

    <footer class="app-footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>浏览器支持</h4>
          <p>Chrome 89+, Edge 89+ (串口功能)</p>
          <p>所有现代浏览器 (浏览器打印)</p>
        </div>
        <div class="footer-section">
          <h4>推荐使用</h4>
          <p>✓ 浏览器打印 - 兼容性最好</p>
          <p>✓ PDF导出 - 便于保存和分享</p>
        </div>
        <div class="footer-section">
          <h4>故障排除</h4>
          <p>如果串口连接失败，请使用浏览器打印</p>
          <p>大多数USB打印机不支持串口通信</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 2rem 1rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.feature-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.app-main {
  flex: 1;
  padding: 2rem 1rem;
  background: #f8f9fa;
}

.app-footer {
  background: #343a40;
  color: #adb5bd;
  padding: 2rem 1rem;
  font-size: 0.9rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-section h4 {
  color: #fff;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.footer-section p {
  margin: 0.25rem 0;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }
}
</style>
