<script setup lang="ts">
import USBPrinter from './components/USBPrinter.vue'
</script>

<template>
  <div id="app">
    <header class="app-header">
      <h1>Vue3 USB打印机 Demo</h1>
      <p>这是一个Vue3连接USB打印机直接打印的演示应用</p>
    </header>

    <main class="app-main">
      <USBPrinter />
    </main>

    <footer class="app-footer">
      <p>支持的浏览器: Chrome 89+, Edge 89+</p>
      <p>需要启用Web Serial API功能</p>
    </footer>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 2rem 1rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 2rem 1rem;
  background: #f8f9fa;
}

.app-footer {
  background: #343a40;
  color: #adb5bd;
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
}

.app-footer p {
  margin: 0.25rem 0;
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }
}
</style>
