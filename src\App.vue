<script setup lang="ts">
import USBThermalPrinter from './components/USBThermalPrinter.vue'
</script>

<template>
  <div id="app">
    <main class="app-main">
      <USBThermalPrinter />
    </main>

    <footer class="app-footer">
      <div class="footer-content">
        <p>🖨️ 专业的USB热敏打印机解决方案 | 支持Chrome 89+ / Edge 89+ | Vue3 + WebHID API</p>
        <p>适用于收银、标签、小票等热敏打印场景</p>
      </div>
    </footer>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 2rem 1rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}



.app-main {
  flex: 1;
  padding: 2rem 1rem;
  background: #f8f9fa;
}

.app-footer {
  background: #343a40;
  color: #adb5bd;
  padding: 1rem;
  font-size: 0.9rem;
  text-align: center;
}

.footer-content p {
  margin: 0.25rem 0;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }
}
</style>
