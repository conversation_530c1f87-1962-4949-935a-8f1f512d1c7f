<template>
  <div class="usb-thermal-printer">
    <div class="printer-header">
      <h2>🖨️ USB热敏打印机</h2>
      <p>支持HID直连和标准USB打印机的热敏打印解决方案</p>
    </div>

    <!-- 打印机连接区域 -->
    <div class="connection-section">
      <h3>1. 连接USB热敏打印机</h3>
      
      <div class="connection-controls">
        <button 
          @click="scanPrinters" 
          :disabled="isScanning"
          class="scan-btn"
        >
          {{ isScanning ? '扫描中...' : '扫描USB热敏打印机' }}
        </button>
        
        <button
          @click="requestNewPrinter"
          :disabled="isConnecting"
          class="request-btn"
        >
          {{ isConnecting ? '连接中...' : '添加新打印机' }}
        </button>

        <button
          @click="debugDeviceInfo"
          class="debug-btn"
          title="调试：显示所有HID设备信息"
        >
          🔍 调试设备信息
        </button>
      </div>

      <!-- 打印机列表 -->
      <div v-if="availablePrinters.length > 0" class="printer-list">
        <h4>检测到的USB热敏打印机：</h4>
        <div class="printer-items">
          <div 
            v-for="printer in availablePrinters" 
            :key="`${printer.vendorId}-${printer.productId}`"
            class="printer-item"
            :class="{ active: selectedPrinter?.device === printer.device }"
          >
            <div class="printer-info">
              <strong>{{ printer.productName }}</strong>
              <small>VID: {{ printer.vendorId.toString(16).toUpperCase() }}, PID: {{ printer.productId.toString(16).toUpperCase() }}</small>
              <span class="status" :class="printer.isConnected ? 'connected' : 'disconnected'">
                {{ printer.isConnected ? '已连接' : '未连接' }}
              </span>
            </div>
            <div class="printer-actions">
              <button 
                v-if="!printer.isConnected"
                @click="connectPrinter(printer)"
                :disabled="isConnecting"
                class="connect-btn"
              >
                连接
              </button>
              <button 
                v-else
                @click="disconnectPrinter"
                class="disconnect-btn"
              >
                断开
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 连接状态 -->
      <div class="connection-status">
        <span v-if="isConnected" class="status connected">✓ 热敏打印机已连接</span>
        <span v-else-if="isConnecting" class="status connecting">⟳ 连接中...</span>
        <span v-else class="status disconnected">○ 未连接热敏打印机</span>
      </div>
    </div>

    <!-- 模板选择区域 -->
    <div class="template-section">
      <h3>2. 选择打印模板</h3>
      <div class="template-grid">
        <div 
          v-for="template in templates" 
          :key="template.id"
          class="template-card"
          :class="{ active: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <h4>{{ template.name }}</h4>
          <div class="template-info">
            <span>宽度: {{ template.width }}mm</span>
            <span>字体: {{ template.fontSize }}px</span>
          </div>
          <div class="template-preview">
            {{ template.content.substring(0, 100) }}...
          </div>
        </div>
      </div>
    </div>

    <!-- 内容编辑区域 -->
    <div class="content-section">
      <h3>3. 编辑打印内容</h3>
      <div class="content-controls">
        <label>
          纸张宽度:
          <select v-model="paperWidth">
            <option value="58">58mm</option>
            <option value="80">80mm</option>
          </select>
        </label>
      </div>
      <textarea 
        v-model="printContent"
        placeholder="请输入要打印的内容，或选择上方的模板..."
        class="content-textarea"
        rows="12"
      ></textarea>
    </div>

    <!-- 打印区域 -->
    <div class="print-section">
      <h3>4. 直接打印</h3>
      <div class="print-controls">
        <button
          @click="directPrint"
          :disabled="!isConnected || !printContent.trim() || isPrinting"
          class="print-btn primary"
        >
          {{ isPrinting ? '打印中...' : '🖨️ 直接打印到热敏纸' }}
        </button>
        <button
          @click="printTestPage"
          :disabled="!isConnected || isPrinting"
          class="print-btn test"
        >
          {{ isPrinting ? '打印中...' : '打印测试页' }}
        </button>
        <button
          @click="browserPrint"
          :disabled="!printContent.trim() || isPrinting"
          class="print-btn browser"
        >
          {{ isPrinting ? '打印中...' : '🖨️ 浏览器打印（备用方案）' }}
        </button>
        <button
          @click="silentPrint"
          :disabled="!printContent.trim() || isPrinting"
          class="print-btn silent"
        >
          {{ isPrinting ? '打印中...' : '🔇 静默打印（实验性）' }}
        </button>
      </div>
      <div class="print-tips">
        <p><strong>💡 打印方式说明：</strong></p>
        <ul>
          <li><strong>🖨️ 直接打印</strong>：HID模式，无弹窗（仅支持HID设备）</li>
          <li><strong>🖨️ 浏览器打印</strong>：标准方式，有打印对话框（兼容所有打印机）</li>
          <li><strong>🔇 静默打印</strong>：实验性功能，尝试减少弹窗（需要浏览器配置）</li>
        </ul>
        <p><strong>🎯 针对你的GP-3120TL：</strong></p>
        <ul>
          <li>推荐使用"浏览器打印"，稳定可靠</li>
          <li>可以尝试"静默打印"，但可能需要浏览器设置</li>
          <li>确保打印机已开机并装好热敏纸</li>
        </ul>
      </div>
    </div>

    <!-- 帮助信息 -->
    <div class="help-section">
      <details>
        <summary>❓ 找不到USB热敏打印机？</summary>
        <div class="help-content">
          <h4>支持的热敏打印机品牌：</h4>
          <ul>
            <li>Epson (爱普生) - TM系列</li>
            <li>佳博 (Gprinter) - GP系列</li>
            <li>汉印 (HPRT) - N系列</li>
            <li>芯烨 (XINYE) - XP系列</li>
            <li>其他使用标准USB接口的热敏打印机</li>
          </ul>
          
          <h4>故障排除：</h4>
          <ol>
            <li>确保使用Chrome 89+或Edge 89+浏览器</li>
            <li>检查打印机是否正确连接到电脑USB口</li>
            <li>确认打印机驱动已正确安装</li>
            <li>点击"🔍 调试设备信息"查看设备是否被识别</li>
            <li>如果设备管理器中显示正常但网页无法连接，尝试重新插拔USB线缆</li>
            <li>对于GP-3120TL：确保在设备管理器中显示为HID设备而不是串口设备</li>
          </ol>

          <h4>特别说明 - GP-3120TL用户：</h4>
          <p><strong>你的GP-3120TL被识别为标准USB打印机，不是HID设备。</strong></p>
          <p>推荐解决方案：</p>
          <ol>
            <li><strong>使用"浏览器打印（备用方案）"</strong> - 这是最可靠的方式</li>
            <li>在Windows中将GP-3120TL设置为默认打印机</li>
            <li>打印时会弹出系统对话框，但可以快速确认</li>
            <li>这种方式完全兼容你的打印机驱动</li>
          </ol>

          <h4>为什么无法直接连接？</h4>
          <p>你的设备信息显示：</p>
          <ul>
            <li>设备类型：USBPRINT（标准USB打印机）</li>
            <li>VID: 0x0471, PID: 0x0055</li>
            <li>WebHID API只能访问HID设备，无法访问USB打印机设备</li>
            <li>这是正常现象，很多热敏打印机都是这种情况</li>
          </ul>

          <h4>如何减少打印弹窗？</h4>
          <p>要使用"静默打印"功能，请按以下步骤配置Chrome浏览器：</p>
          <ol>
            <li>在Chrome地址栏输入：<code>chrome://settings/content/printing</code></li>
            <li>或者：设置 → 隐私设置和安全性 → 网站设置 → 打印</li>
            <li>将本网站添加到"允许打印"列表中</li>
            <li>在打印机设置中将GP-3120TL设为默认打印机</li>
            <li>重新加载页面，然后尝试"静默打印"功能</li>
          </ol>
          <p><strong>注意：</strong>即使配置了静默打印，某些情况下仍可能出现确认对话框，这是浏览器的安全限制。</p>
        </div>
      </details>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { usbThermalPrinterService, type ThermalPrinterInfo, type ThermalPrintTemplate } from '../services/USBThermalPrinterService'

// 响应式数据
const availablePrinters = ref<ThermalPrinterInfo[]>([])
const selectedPrinter = ref<ThermalPrinterInfo | null>(null)
const isConnected = ref(false)
const isScanning = ref(false)
const isConnecting = ref(false)
const isPrinting = ref(false)

const templates = ref<ThermalPrintTemplate[]>([])
const selectedTemplate = ref<ThermalPrintTemplate | null>(null)
const printContent = ref('')
const paperWidth = ref<number>(58)

// 扫描USB热敏打印机
const scanPrinters = async () => {
  if (isScanning.value) return
  
  try {
    isScanning.value = true
    
    if (!usbThermalPrinterService.constructor.isSupported()) {
      alert('您的浏览器不支持WebHID API，请使用Chrome 89+或Edge 89+')
      return
    }

    const printers = await usbThermalPrinterService.getConnectedPrinters()
    availablePrinters.value = printers
    
    if (printers.length === 0) {
      alert('未检测到USB热敏打印机\n\n请确保：\n1. 打印机已连接到电脑\n2. 打印机驱动已安装\n3. 使用支持的热敏打印机品牌')
    } else {
      console.log(`检测到 ${printers.length} 台USB热敏打印机`)
    }
    
  } catch (error) {
    console.error('扫描打印机失败:', error)
    alert('扫描失败: ' + (error as Error).message)
  } finally {
    isScanning.value = false
  }
}

// 请求新的打印机
const requestNewPrinter = async () => {
  if (isConnecting.value) return
  
  try {
    isConnecting.value = true
    
    const printer = await usbThermalPrinterService.requestPrinter()
    
    // 检查是否已经在列表中
    const existing = availablePrinters.value.find(p => 
      p.vendorId === printer.vendorId && p.productId === printer.productId
    )
    
    if (!existing) {
      availablePrinters.value.push(printer)
    }
    
    // 自动连接新添加的打印机
    await connectPrinter(printer)
    
  } catch (error) {
    console.error('添加打印机失败:', error)
    if ((error as Error).message.includes('用户取消')) {
      // 用户取消选择，不显示错误
      return
    }
    alert('添加打印机失败: ' + (error as Error).message)
  } finally {
    isConnecting.value = false
  }
}

// 连接打印机
const connectPrinter = async (printer: ThermalPrinterInfo) => {
  if (isConnecting.value) return
  
  try {
    isConnecting.value = true
    
    await usbThermalPrinterService.connect(printer)
    
    selectedPrinter.value = printer
    printer.isConnected = true
    isConnected.value = true
    
    console.log('USB热敏打印机连接成功:', printer.productName)
    
  } catch (error) {
    console.error('连接打印机失败:', error)
    alert('连接失败: ' + (error as Error).message)
  } finally {
    isConnecting.value = false
  }
}

// 断开打印机
const disconnectPrinter = async () => {
  try {
    await usbThermalPrinterService.disconnect()
    
    if (selectedPrinter.value) {
      selectedPrinter.value.isConnected = false
    }
    
    selectedPrinter.value = null
    isConnected.value = false
    
    console.log('打印机已断开连接')
    
  } catch (error) {
    console.error('断开连接失败:', error)
    alert('断开连接失败: ' + (error as Error).message)
  }
}

// 选择模板
const selectTemplate = (template: ThermalPrintTemplate) => {
  selectedTemplate.value = template
  printContent.value = template.content
  paperWidth.value = template.width
}

// 直接打印
const directPrint = async () => {
  if (!isConnected.value || !printContent.value.trim() || isPrinting.value) return
  
  try {
    isPrinting.value = true
    
    await usbThermalPrinterService.printText(printContent.value, paperWidth.value)
    
    console.log('打印完成')
    alert('✅ 打印完成！请查看热敏打印机输出。')
    
  } catch (error) {
    console.error('打印失败:', error)
    alert('❌ 打印失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 打印测试页
const printTestPage = async () => {
  if (!isConnected.value || isPrinting.value) return

  try {
    isPrinting.value = true

    const testTemplate = templates.value.find(t => t.id === 'test')
    if (testTemplate) {
      await usbThermalPrinterService.printTemplate(testTemplate)
      console.log('测试页打印完成')
      alert('✅ 测试页打印完成！')
    }

  } catch (error) {
    console.error('打印测试页失败:', error)
    alert('❌ 打印测试页失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 浏览器打印（备用方案）
const browserPrint = async () => {
  if (!printContent.value.trim() || isPrinting.value) return

  try {
    isPrinting.value = true

    // 创建打印窗口
    const printWindow = window.open('', '_blank', 'width=800,height=600')
    if (!printWindow) {
      alert('无法打开打印窗口，请检查浏览器弹窗设置')
      return
    }

    // 生成打印内容
    const printHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>热敏打印</title>
        <style>
          @page {
            size: ${paperWidth.value}mm auto;
            margin: 5mm;
          }
          body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 0;
            width: ${paperWidth.value - 10}mm;
          }
          .print-content {
            white-space: pre-wrap;
            word-wrap: break-word;
          }
        </style>
      </head>
      <body>
        <div class="print-content">${printContent.value.replace(/\n/g, '<br>')}</div>
        <script>
          window.onload = function() {
            window.print();
            setTimeout(function() {
              window.close();
            }, 1000);
          }
        <\/script>
      </body>
      </html>
    `

    printWindow.document.write(printHTML)
    printWindow.document.close()

    console.log('浏览器打印完成')

  } catch (error) {
    console.error('浏览器打印失败:', error)
    alert('❌ 浏览器打印失败: ' + (error as Error).message)
  } finally {
    isPrinting.value = false
  }
}

// 静默打印（实验性）
const silentPrint = async () => {
  if (!printContent.value.trim() || isPrinting.value) return

  try {
    isPrinting.value = true

    // 创建隐藏的iframe进行打印
    const iframe = document.createElement('iframe')
    iframe.style.position = 'absolute'
    iframe.style.left = '-9999px'
    iframe.style.top = '-9999px'
    iframe.style.width = '1px'
    iframe.style.height = '1px'
    iframe.style.visibility = 'hidden'

    document.body.appendChild(iframe)

    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
    if (!iframeDoc) {
      throw new Error('无法创建打印文档')
    }

    // 生成打印内容
    const printHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>热敏打印</title>
        <style>
          @page {
            size: ${paperWidth.value}mm auto;
            margin: 2mm;
          }
          @media print {
            body { -webkit-print-color-adjust: exact; }
          }
          body {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.1;
            margin: 0;
            padding: 2mm;
            width: ${paperWidth.value - 4}mm;
            background: white;
          }
          .print-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            color: black;
          }
        </style>
      </head>
      <body>
        <div class="print-content">${printContent.value.replace(/\n/g, '<br>')}</div>
      </body>
      </html>
    `

    iframeDoc.write(printHTML)
    iframeDoc.close()

    // 等待内容加载完成
    await new Promise(resolve => {
      iframe.onload = resolve
      setTimeout(resolve, 500) // 备用超时
    })

    // 尝试静默打印
    try {
      iframe.contentWindow?.focus()
      iframe.contentWindow?.print()

      // 延迟清理
      setTimeout(() => {
        document.body.removeChild(iframe)
      }, 2000)

      console.log('静默打印完成')
      alert('✅ 静默打印已发送！如果没有自动打印，请检查浏览器设置。')

    } catch (printError) {
      // 如果静默打印失败，回退到普通打印
      document.body.removeChild(iframe)
      throw new Error('静默打印失败，请使用浏览器打印方案')
    }

  } catch (error) {
    console.error('静默打印失败:', error)
    alert('❌ 静默打印失败: ' + (error as Error).message + '\n\n请使用"浏览器打印"方案。')
  } finally {
    isPrinting.value = false
  }
}

// 调试设备信息
const debugDeviceInfo = async () => {
  if (!usbThermalPrinterService.constructor.isSupported()) {
    alert('您的浏览器不支持WebHID API')
    return
  }

  try {
    // 获取所有已连接的HID设备
    const allDevices = await navigator.hid.getDevices()
    console.log('所有已连接的HID设备:', allDevices)

    let deviceInfo = '=== 所有已连接的HID设备 ===\n\n'

    if (allDevices.length === 0) {
      deviceInfo += '没有找到已连接的HID设备。\n\n'
      deviceInfo += '你的GP-3120TL显示为标准USB打印机设备，不是HID设备。\n'
      deviceInfo += '这意味着无法使用WebHID API直接连接。\n\n'
      deviceInfo += '解决方案：\n'
      deviceInfo += '1. 使用"浏览器打印（备用方案）"按钮\n'
      deviceInfo += '2. 这将通过系统打印机驱动进行打印\n'
      deviceInfo += '3. 虽然会有打印对话框，但可以设置默认打印机来简化操作'
    } else {
      allDevices.forEach((device, index) => {
        deviceInfo += `设备 ${index + 1}:\n`
        deviceInfo += `  产品名称: ${device.productName || '未知'}\n`
        deviceInfo += `  厂商名称: ${device.manufacturerName || '未知'}\n`
        deviceInfo += `  厂商ID (VID): 0x${device.vendorId.toString(16).padStart(4, '0').toUpperCase()}\n`
        deviceInfo += `  产品ID (PID): 0x${device.productId.toString(16).padStart(4, '0').toUpperCase()}\n`
        deviceInfo += `  是否已打开: ${device.opened ? '是' : '否'}\n`
        deviceInfo += `  集合数量: ${device.collections?.length || 0}\n`

        // 检查是否是佳博设备
        if (device.vendorId === 0x1fc9) {
          deviceInfo += `  ✅ 这是佳博(Gprinter)设备！\n`
        }

        deviceInfo += '\n'
      })

      deviceInfo += '\n如果你的GP-3120TL在上面的列表中，请记录其VID和PID信息。'
      deviceInfo += '\n\n如果没有找到GP-3120TL，请使用"浏览器打印"功能。'
    }

    alert(deviceInfo)

  } catch (error) {
    console.error('获取设备信息失败:', error)
    alert('获取设备信息失败: ' + (error as Error).message)
  }
}

// 组件挂载
onMounted(async () => {
  // 加载预设模板
  templates.value = usbThermalPrinterService.getPresetTemplates()
  
  // 检查浏览器支持
  if (!usbThermalPrinterService.constructor.isSupported()) {
    console.warn('当前浏览器不支持WebHID API')
    alert('⚠️ 当前浏览器不支持WebHID API\n\n请使用以下浏览器：\n• Chrome 89+\n• Edge 89+')
    return
  }
  
  // 自动扫描已连接的打印机
  await scanPrinters()
})

// 组件卸载
onUnmounted(async () => {
  if (isConnected.value) {
    await disconnectPrinter()
  }
})
</script>

<style scoped>
.usb-thermal-printer {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.printer-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.printer-header h2 {
  margin: 0 0 10px 0;
  font-size: 1.8rem;
}

.printer-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.connection-section, .template-section, .content-section, .print-section, .help-section {
  margin-bottom: 30px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.connection-section h3, .template-section h3, .content-section h3, .print-section h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.2rem;
}

.connection-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.scan-btn, .request-btn, .debug-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.scan-btn {
  background: #007bff;
  color: white;
}

.scan-btn:hover:not(:disabled) {
  background: #0056b3;
}

.request-btn {
  background: #28a745;
  color: white;
}

.request-btn:hover:not(:disabled) {
  background: #1e7e34;
}

.debug-btn {
  background: #6f42c1;
  color: white;
}

.debug-btn:hover:not(:disabled) {
  background: #5a32a3;
}

.scan-btn:disabled, .request-btn:disabled, .debug-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.printer-list h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1rem;
}

.printer-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.printer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.printer-item:hover {
  border-color: #007bff;
}

.printer-item.active {
  border-color: #28a745;
  background-color: #f8fff9;
}

.printer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.printer-info strong {
  font-size: 14px;
  color: #495057;
}

.printer-info small {
  font-size: 12px;
  color: #6c757d;
}

.printer-info .status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  width: fit-content;
}

.status.connected {
  background: #d4edda;
  color: #155724;
}

.status.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.status.connecting {
  background: #fff3cd;
  color: #856404;
}

.connect-btn, .disconnect-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.connect-btn {
  background: #28a745;
  color: white;
}

.disconnect-btn {
  background: #dc3545;
  color: white;
}

.connection-status {
  margin-top: 15px;
  text-align: center;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.template-card {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: #007bff;
}

.template-card.active {
  border-color: #28a745;
  background-color: #f8fff9;
}

.template-card h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.template-info {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #6c757d;
}

.template-preview {
  font-size: 11px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  max-height: 60px;
  overflow: hidden;
}

.content-controls {
  margin-bottom: 15px;
}

.content-controls label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #495057;
}

.content-controls select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.content-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  resize: vertical;
}

.print-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.print-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.print-btn.primary {
  background: #28a745;
  color: white;
  font-weight: bold;
}

.print-btn.primary:hover:not(:disabled) {
  background: #1e7e34;
}

.print-btn.test {
  background: #6f42c1;
  color: white;
}

.print-btn.test:hover:not(:disabled) {
  background: #5a32a3;
}

.print-btn.browser {
  background: #fd7e14;
  color: white;
}

.print-btn.browser:hover:not(:disabled) {
  background: #e8590c;
}

.print-btn.silent {
  background: #20c997;
  color: white;
}

.print-btn.silent:hover:not(:disabled) {
  background: #1aa179;
}

.print-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.print-tips {
  background: #e7f3ff;
  border: 1px solid #b8daff;
  border-radius: 4px;
  padding: 15px;
  font-size: 13px;
}

.print-tips p {
  margin: 0 0 8px 0;
  color: #004085;
}

.print-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #004085;
}

.print-tips li {
  margin-bottom: 4px;
}

.help-section {
  background: #f8f9fa;
}

.help-section details summary {
  cursor: pointer;
  font-weight: bold;
  color: #495057;
  padding: 10px 0;
}

.help-content {
  padding-top: 15px;
  font-size: 13px;
  color: #495057;
}

.help-content h4 {
  margin: 0 0 8px 0;
  color: #495057;
}

.help-content ul, .help-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .usb-thermal-printer {
    padding: 15px;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .connection-controls, .print-controls {
    flex-direction: column;
  }
  
  .printer-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
