/**
 * USB热敏打印机服务
 * 使用WebHID API直接连接USB热敏打印机，实现真正的无弹窗直接打印
 */

export interface ThermalPrinterInfo {
  device: HIDDevice
  productName: string
  vendorId: number
  productId: number
  isConnected: boolean
}

export interface ThermalPrintTemplate {
  id: string
  name: string
  content: string
  width: number // 58mm or 80mm
  fontSize: number
}

interface PrinterFilter {
  vendorId: number
  productId?: number // 可选，如果不指定则匹配该厂商的所有设备
}

export class USBThermalPrinterService {
  private connectedDevice: HIDDevice | null = null
  private isConnected = false

  // 常见热敏打印机的USB VID/PID
  private readonly THERMAL_PRINTER_FILTERS: PrinterFilter[] = [
    // Epson热敏打印机
    { vendorId: 0x04b8, productId: 0x0202 }, // TM-T88V
    { vendorId: 0x04b8, productId: 0x0203 }, // TM-T88VI
    { vendorId: 0x04b8, productId: 0x0e15 }, // TM-T20
    { vendorId: 0x04b8, productId: 0x0e28 }, // TM-T82
    
    // 佳博热敏打印机
    { vendorId: 0x1fc9, productId: 0x2016 }, // GP-58130IVC
    { vendorId: 0x1fc9, productId: 0x2017 }, // GP-80250I
    { vendorId: 0x1fc9, productId: 0x2018 }, // GP-3120TL
    { vendorId: 0x1fc9, productId: 0x2019 }, // GP-3120TL (另一个可能的PID)
    { vendorId: 0x1fc9, productId: 0x201a }, // GP-3120TL (另一个可能的PID)
    { vendorId: 0x1fc9, productId: 0x201b }, // GP-3120TL (另一个可能的PID)
    
    // 汉印热敏打印机
    { vendorId: 0x0fe6, productId: 0x811e }, // N31
    { vendorId: 0x0fe6, productId: 0x811f }, // N41
    
    // 芯烨热敏打印机
    { vendorId: 0x0519, productId: 0x2013 }, // XP-58IIH
    { vendorId: 0x0519, productId: 0x2014 }, // XP-80C
    
    // 通用USB转串口芯片（很多热敏打印机使用）
    { vendorId: 0x1a86, productId: 0x7523 }, // CH340
    { vendorId: 0x0403, productId: 0x6001 }, // FTDI
    { vendorId: 0x10c4, productId: 0xea60 }, // CP210x
    
    // 其他常见热敏打印机
    { vendorId: 0x067b, productId: 0x2303 }, // PL2303
    { vendorId: 0x1659, productId: 0x8965 }, //

    // 通用厂商过滤器（匹配所有该厂商的设备）
    { vendorId: 0x1fc9 }, // 佳博所有设备
  ]

  /**
   * 检查浏览器是否支持WebHID API
   */
  static isSupported(): boolean {
    return 'hid' in navigator
  }

  /**
   * 获取已连接的USB热敏打印机列表
   */
  async getConnectedPrinters(): Promise<ThermalPrinterInfo[]> {
    if (!USBThermalPrinterService.isSupported()) {
      throw new Error('浏览器不支持WebHID API，请使用Chrome 89+或Edge 89+')
    }

    try {
      const devices = await navigator.hid.getDevices()
      const thermalPrinters: ThermalPrinterInfo[] = []

      for (const device of devices) {
        // 检查是否是热敏打印机
        if (this.isThermalPrinter(device)) {
          thermalPrinters.push({
            device,
            productName: device.productName || `热敏打印机 (${device.vendorId.toString(16)}:${device.productId.toString(16)})`,
            vendorId: device.vendorId,
            productId: device.productId,
            isConnected: device.opened
          })
        }
      }

      return thermalPrinters
    } catch (error) {
      console.error('获取USB设备失败:', error)
      throw new Error('获取USB设备失败: ' + (error as Error).message)
    }
  }

  /**
   * 请求连接新的USB热敏打印机
   */
  async requestPrinter(): Promise<ThermalPrinterInfo> {
    if (!USBThermalPrinterService.isSupported()) {
      throw new Error('浏览器不支持WebHID API，请使用Chrome 89+或Edge 89+')
    }

    try {
      // 先尝试使用过滤器
      let devices = await navigator.hid.requestDevice({
        filters: this.THERMAL_PRINTER_FILTERS
      })

      // 如果没有找到设备，尝试不使用过滤器（显示所有HID设备）
      if (devices.length === 0) {
        console.log('使用过滤器未找到设备，尝试显示所有HID设备...')
        devices = await navigator.hid.requestDevice({
          filters: [] // 空过滤器显示所有HID设备
        })
      }

      if (devices.length === 0) {
        throw new Error('未选择设备')
      }

      const device = devices[0]

      // 输出设备信息用于调试
      console.log('选择的设备信息:', {
        productName: device.productName,
        vendorId: device.vendorId,
        productId: device.productId,
        vendorIdHex: '0x' + device.vendorId.toString(16).padStart(4, '0'),
        productIdHex: '0x' + device.productId.toString(16).padStart(4, '0'),
        collections: device.collections
      })

      return {
        device,
        productName: device.productName || `热敏打印机 (${device.vendorId.toString(16)}:${device.productId.toString(16)})`,
        vendorId: device.vendorId,
        productId: device.productId,
        isConnected: false
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('No device selected')) {
        throw new Error('用户取消了设备选择')
      }
      throw new Error('请求USB设备失败: ' + (error as Error).message)
    }
  }

  /**
   * 连接到指定的热敏打印机
   */
  async connect(printerInfo: ThermalPrinterInfo): Promise<void> {
    try {
      if (!printerInfo.device.opened) {
        await printerInfo.device.open()
      }
      
      this.connectedDevice = printerInfo.device
      this.isConnected = true
      
      console.log('USB热敏打印机连接成功:', printerInfo.productName)
    } catch (error) {
      console.error('连接热敏打印机失败:', error)
      throw new Error('连接失败: ' + (error as Error).message)
    }
  }

  /**
   * 断开打印机连接
   */
  async disconnect(): Promise<void> {
    if (this.connectedDevice && this.connectedDevice.opened) {
      await this.connectedDevice.close()
    }
    this.connectedDevice = null
    this.isConnected = false
  }

  /**
   * 检查是否已连接
   */
  getConnectionStatus(): boolean {
    return this.isConnected && this.connectedDevice !== null
  }

  /**
   * 直接打印文本到热敏打印机
   */
  async printText(content: string, width: number = 58): Promise<void> {
    if (!this.isConnected || !this.connectedDevice) {
      throw new Error('打印机未连接')
    }

    try {
      // 生成ESC/POS命令
      const commands = this.generateESCPOSCommands(content, width)
      
      // 发送数据到打印机
      await this.sendData(commands)
      
      console.log('打印完成')
    } catch (error) {
      console.error('打印失败:', error)
      throw new Error('打印失败: ' + (error as Error).message)
    }
  }

  /**
   * 使用模板打印
   */
  async printTemplate(template: ThermalPrintTemplate): Promise<void> {
    await this.printText(template.content, template.width)
  }

  /**
   * 生成ESC/POS命令
   */
  private generateESCPOSCommands(content: string, width: number): Uint8Array {
    const commands: number[] = []
    
    // 初始化打印机
    commands.push(0x1B, 0x40) // ESC @
    
    // 设置字符编码为UTF-8
    commands.push(0x1B, 0x74, 0x06)
    
    // 设置字体大小（根据纸张宽度调整）
    if (width === 58) {
      commands.push(0x1D, 0x21, 0x00) // 正常大小
    } else {
      commands.push(0x1D, 0x21, 0x11) // 稍大一些
    }
    
    // 设置左对齐
    commands.push(0x1B, 0x61, 0x00)
    
    // 添加文本内容
    const encoder = new TextEncoder()
    const textBytes = encoder.encode(content)
    commands.push(...Array.from(textBytes))
    
    // 换行
    commands.push(0x0A, 0x0A)
    
    // 切纸（如果支持）
    commands.push(0x1D, 0x56, 0x42, 0x00)
    
    return new Uint8Array(commands)
  }

  /**
   * 发送数据到打印机
   */
  private async sendData(data: Uint8Array): Promise<void> {
    if (!this.connectedDevice) {
      throw new Error('设备未连接')
    }

    try {
      // 将数据分块发送（HID设备通常有数据包大小限制）
      const chunkSize = 64 // 大多数HID设备的报告大小
      
      for (let i = 0; i < data.length; i += chunkSize) {
        const chunk = data.slice(i, i + chunkSize)
        const reportData = new Uint8Array(chunkSize)
        reportData.set(chunk)
        
        await this.connectedDevice.sendReport(0, reportData)
        
        // 短暂延迟，确保数据正确传输
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    } catch (error) {
      console.error('发送数据失败:', error)
      throw new Error('发送数据失败: ' + (error as Error).message)
    }
  }

  /**
   * 判断设备是否是热敏打印机
   */
  private isThermalPrinter(device: HIDDevice): boolean {
    return this.THERMAL_PRINTER_FILTERS.some(filter => {
      // 如果过滤器只指定了vendorId，则匹配该厂商的所有设备
      if (filter.productId === undefined) {
        return device.vendorId === filter.vendorId
      }
      // 否则需要精确匹配vendorId和productId
      return device.vendorId === filter.vendorId && device.productId === filter.productId
    })
  }

  /**
   * 获取预设模板
   */
  getPresetTemplates(): ThermalPrintTemplate[] {
    const now = new Date()
    const dateStr = now.toLocaleDateString()
    const timeStr = now.toLocaleString()

    return [
      {
        id: 'receipt',
        name: '收银小票',
        width: 58,
        fontSize: 12,
        content: `================================
           欢迎光临
================================
商品名称          数量    金额
--------------------------------
苹果              2kg    ¥12.00
香蕉              1kg    ¥8.00
橙子              3kg    ¥15.00
--------------------------------
小计：                   ¥35.00
优惠：                   ¥-2.00
合计：                   ¥33.00
收款：                   ¥50.00
找零：                   ¥17.00
================================
收银员：001        ${timeStr}
谢谢惠顾，欢迎再次光临！
================================`
      },
      {
        id: 'order',
        name: '订单小票',
        width: 58,
        fontSize: 12,
        content: `================================
          外卖订单
================================
订单号：202412310001
下单时间：${timeStr}
--------------------------------
商品明细：
麻辣香锅(大份)     x1  ¥28.00
可乐(中杯)         x2  ¥12.00
--------------------------------
餐具费：                 ¥1.00
配送费：                 ¥3.00
合计：                  ¥44.00
================================
配送地址：
XX小区XX栋XX号
联系电话：138****1234
备注：少辣，多菜
================================
预计送达：30分钟
================================`
      },
      {
        id: 'label',
        name: '商品标签',
        width: 80,
        fontSize: 14,
        content: `商品标签
------------------------
名称：精选苹果
规格：500g/袋
价格：¥6.00
产地：山东烟台
生产日期：${dateStr}
保质期：7天
------------------------
扫码查看详情`
      },
      {
        id: 'shipping',
        name: '快递单',
        width: 80,
        fontSize: 12,
        content: `================================
          快递面单
================================
运单号：SF1234567890123
寄件时间：${timeStr}
--------------------------------
寄件人信息：
姓名：张三
电话：138****1234
地址：北京市朝阳区XX路XX号

收件人信息：
姓名：李四
电话：139****5678
地址：上海市浦东新区XX街XX号
--------------------------------
物品信息：
数码产品 x1
重量：0.5kg
代收货款：¥299.00
================================
请妥善保管此单据
================================`
      },
      {
        id: 'queue',
        name: '排队号码',
        width: 58,
        fontSize: 16,
        content: `================================
          排队取号
================================

        您的号码是：

           A088

--------------------------------
前面还有 12 人在等待
预计等待时间：25分钟
--------------------------------
取号时间：${timeStr}
请耐心等待，注意听号
================================`
      },
      {
        id: 'test',
        name: '打印测试',
        width: 58,
        fontSize: 12,
        content: `打印机测试页
================
测试时间：${timeStr}
打印机状态：正常
字体显示：正常
字符对齐：居左对齐
中文字符：测试正常
数字字符：1234567890
特殊字符：!@#$%^&*()
================
测试完成！`
      }
    ]
  }
}

// 创建全局实例
export const usbThermalPrinterService = new USBThermalPrinterService()
