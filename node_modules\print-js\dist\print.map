{"version": 3, "sources": ["webpack://printJS/webpack/universalModuleDefinition", "webpack://printJS/webpack/bootstrap", "webpack://printJS/./src/index.js", "webpack://printJS/./src/js/browser.js", "webpack://printJS/./src/js/functions.js", "webpack://printJS/./src/js/html.js", "webpack://printJS/./src/js/image.js", "webpack://printJS/./src/js/init.js", "webpack://printJS/./src/js/json.js", "webpack://printJS/./src/js/modal.js", "webpack://printJS/./src/js/pdf.js", "webpack://printJS/./src/js/print.js", "webpack://printJS/./src/js/raw-html.js", "webpack://printJS/./src/sass/index.scss"], "names": ["printJS", "print", "init", "window", "Browser", "isFirefox", "InstallTrigger", "isIE", "navigator", "userAgent", "indexOf", "document", "documentMode", "isEdge", "StyleMedia", "isChrome", "context", "chrome", "<PERSON><PERSON><PERSON><PERSON>", "Object", "prototype", "toString", "call", "HTMLElement", "toLowerCase", "isIOSChrome", "addWrapper", "htmlData", "params", "bodyStyle", "font", "font_size", "capitalizePrint", "obj", "char<PERSON>t", "toUpperCase", "slice", "collectStyles", "element", "win", "defaultView", "elementStyle", "styles", "getComputedStyle", "key", "length", "targetStyles", "targetStyle", "targetStylesMatch", "getPropertyValue", "max<PERSON><PERSON><PERSON>", "value", "i", "addHeader", "printElement", "headerContainer", "createElement", "isRawHTML", "header", "innerHTML", "headerElement", "headerNode", "createTextNode", "append<PERSON><PERSON><PERSON>", "setAttribute", "headerStyle", "insertBefore", "childNodes", "cleanUp", "showModal", "Modal", "close", "onLoadingEnd", "onLoadingStart", "URL", "revokeObjectURL", "printable", "event", "handler", "removeEventListener", "onPrintDialogClose", "iframe", "getElementById", "frameId", "remove", "addEventListener", "raw", "regexHtml", "RegExp", "test", "printFrame", "isHtmlElement", "console", "error", "printableElement", "cloneElement", "Print", "send", "clone", "cloneNode", "childNodesArray", "Array", "ignoreElements", "id", "clone<PERSON><PERSON><PERSON><PERSON>", "scanStyles", "nodeType", "tagName", "getContext", "drawImage", "constructor", "for<PERSON>ach", "src", "img", "imageStyle", "fullyQualifiedSrc", "imageWrapper", "printTypes", "fallbackPrintable", "type", "properties", "gridHeaderStyle", "gridStyle", "onError", "onIncompatibleBrowser", "modalMessage", "documentTitle", "repeatTableHeader", "css", "style", "base64", "onPdfOpen", "honorMarginPadding", "honorColor", "args", "arguments", "undefined", "Error", "encodeURI", "k", "show", "usedFrame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "srcdoc", "isArray", "file", "info", "open", "focus", "Pdf", "Image", "Html", "RawHtml", "Json", "map", "property", "field", "displayName", "columnSize", "jsonToHTML", "data", "a", "n", "stringData", "split", "p", "modalStyle", "printModal", "contentDiv", "closeButton", "spinner", "messageNode", "getElementsByTagName", "bytesArray", "Uint8Array", "from", "atob", "c", "charCodeAt", "createBlobAndPrint", "location", "origin", "req", "XMLHttpRequest", "responseType", "statusText", "status", "response", "localPdf", "Blob", "createObjectURL", "iframeElement", "onload", "setTimeout", "performPrint", "printDocument", "contentWindow", "contentDocument", "body", "head", "images", "loadIframeImages", "then", "execCommand", "e", "visibility", "left", "promises", "image", "href", "loadIframeImage", "Promise", "all", "resolve", "pollImage", "naturalWidth", "complete"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA,IAAMA,OAAO,GAAGC,gDAAK,CAACC,IAAtB;;AAEA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;AACjCA,QAAM,CAACH,OAAP,GAAiBA,OAAjB;AACD;;AAEcA,sEAAf,E;;;;;;;;;;;;ACTA;AAAA,IAAMI,OAAO,GAAG;AACd;AACAC,WAAS,EAAE,qBAAM;AACf,WAAO,OAAOC,cAAP,KAA0B,WAAjC;AACD,GAJa;AAKd;AACAC,MAAI,EAAE,gBAAM;AACV,WAAOC,SAAS,CAACC,SAAV,CAAoBC,OAApB,CAA4B,MAA5B,MAAwC,CAAC,CAAzC,IAA8C,CAAC,CAACC,QAAQ,CAACC,YAAhE;AACD,GARa;AASd;AACAC,QAAM,EAAE,kBAAM;AACZ,WAAO,CAACT,OAAO,CAACG,IAAR,EAAD,IAAmB,CAAC,CAACJ,MAAM,CAACW,UAAnC;AACD,GAZa;AAad;AACAC,UAAQ,EAAE,oBAAsB;AAAA,QAArBC,OAAqB,uEAAXb,MAAW;AAC9B,WAAO,CAAC,CAACa,OAAO,CAACC,MAAjB;AACD,GAhBa;AAiBd;AACAC,UAAQ,EAAE,oBAAM;AACd,WAAOC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BnB,MAAM,CAACoB,WAAtC,EAAmDb,OAAnD,CAA2D,aAA3D,IAA4E,CAA5E,IACHF,SAAS,CAACC,SAAV,CAAoBe,WAApB,GAAkCd,OAAlC,CAA0C,QAA1C,MAAwD,CAAC,CAD7D;AAED,GArBa;AAsBd;AACAe,aAAW,EAAE,uBAAM;AACjB,WAAOjB,SAAS,CAACC,SAAV,CAAoBe,WAApB,GAAkCd,OAAlC,CAA0C,OAA1C,MAAuD,CAAC,CAA/D;AACD;AAzBa,CAAhB;AA4BeN,sEAAf,E;;;;;;;;;;;;;;;;;;;;;;;AC5BA;AACA;AAEO,SAASsB,UAAT,CAAqBC,QAArB,EAA+BC,MAA/B,EAAuC;AAC5C,MAAMC,SAAS,GAAG,iBAAiBD,MAAM,CAACE,IAAxB,GAA+B,0BAA/B,GAA4DF,MAAM,CAACG,SAAnE,GAA+E,0BAAjG;AACA,SAAO,iBAAiBF,SAAjB,GAA6B,IAA7B,GAAoCF,QAApC,GAA+C,QAAtD;AACD;AAEM,SAASK,eAAT,CAA0BC,GAA1B,EAA+B;AACpC,SAAOA,GAAG,CAACC,MAAJ,CAAW,CAAX,EAAcC,WAAd,KAA8BF,GAAG,CAACG,KAAJ,CAAU,CAAV,CAArC;AACD;AAEM,SAASC,aAAT,CAAwBC,OAAxB,EAAiCV,MAAjC,EAAyC;AAC9C,MAAMW,GAAG,GAAG5B,QAAQ,CAAC6B,WAAT,IAAwBrC,MAApC,CAD8C,CAG9C;;AACA,MAAIsC,YAAY,GAAG,EAAnB,CAJ8C,CAM9C;;AACA,MAAMC,MAAM,GAAGH,GAAG,CAACI,gBAAJ,CAAqBL,OAArB,EAA8B,EAA9B,CAAf;;AAEA,OAAK,IAAIM,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGF,MAAM,CAACG,MAA/B,EAAuCD,GAAG,EAA1C,EAA8C;AAC5C;AACA,QAAIhB,MAAM,CAACkB,YAAP,CAAoBpC,OAApB,CAA4B,GAA5B,MAAqC,CAAC,CAAtC,IAA2CkB,MAAM,CAACmB,WAAP,CAAmBrC,OAAnB,CAA2BgC,MAAM,CAACE,GAAD,CAAjC,MAA4C,CAAC,CAAxF,IAA6FI,iBAAiB,CAACpB,MAAM,CAACkB,YAAR,EAAsBJ,MAAM,CAACE,GAAD,CAA5B,CAAlH,EAAsJ;AACpJ,UAAIF,MAAM,CAACO,gBAAP,CAAwBP,MAAM,CAACE,GAAD,CAA9B,CAAJ,EAA0CH,YAAY,IAAIC,MAAM,CAACE,GAAD,CAAN,GAAc,GAAd,GAAoBF,MAAM,CAACO,gBAAP,CAAwBP,MAAM,CAACE,GAAD,CAA9B,CAApB,GAA2D,GAA3E;AAC3C;AACF,GAd6C,CAgB9C;;;AACAH,cAAY,IAAI,gBAAgBb,MAAM,CAACsB,QAAvB,GAAkC,4BAAlC,GAAiEtB,MAAM,CAACG,SAAxE,GAAoF,cAApG;AAEA,SAAOU,YAAP;AACD;;AAED,SAASO,iBAAT,CAA4BN,MAA5B,EAAoCS,KAApC,EAA2C;AACzC,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,MAAM,CAACG,MAA3B,EAAmCO,CAAC,EAApC,EAAwC;AACtC,QAAI,QAAOD,KAAP,MAAiB,QAAjB,IAA6BA,KAAK,CAACzC,OAAN,CAAcgC,MAAM,CAACU,CAAD,CAApB,MAA6B,CAAC,CAA/D,EAAkE,OAAO,IAAP;AACnE;;AACD,SAAO,KAAP;AACD;;AAEM,SAASC,SAAT,CAAoBC,YAApB,EAAkC1B,MAAlC,EAA0C;AAC/C;AACA,MAAM2B,eAAe,GAAG5C,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAAxB,CAF+C,CAI/C;;AACA,MAAIC,SAAS,CAAC7B,MAAM,CAAC8B,MAAR,CAAb,EAA8B;AAC5BH,mBAAe,CAACI,SAAhB,GAA4B/B,MAAM,CAAC8B,MAAnC;AACD,GAFD,MAEO;AACL;AACA,QAAME,aAAa,GAAGjD,QAAQ,CAAC6C,aAAT,CAAuB,IAAvB,CAAtB,CAFK,CAIL;;AACA,QAAMK,UAAU,GAAGlD,QAAQ,CAACmD,cAAT,CAAwBlC,MAAM,CAAC8B,MAA/B,CAAnB,CALK,CAOL;;AACAE,iBAAa,CAACG,WAAd,CAA0BF,UAA1B;AACAD,iBAAa,CAACI,YAAd,CAA2B,OAA3B,EAAoCpC,MAAM,CAACqC,WAA3C;AACAV,mBAAe,CAACQ,WAAhB,CAA4BH,aAA5B;AACD;;AAEDN,cAAY,CAACY,YAAb,CAA0BX,eAA1B,EAA2CD,YAAY,CAACa,UAAb,CAAwB,CAAxB,CAA3C;AACD;AAEM,SAASC,OAAT,CAAkBxC,MAAlB,EAA0B;AAC/B;AACA,MAAIA,MAAM,CAACyC,SAAX,EAAsBC,8CAAK,CAACC,KAAN,GAFS,CAI/B;;AACA,MAAI3C,MAAM,CAAC4C,YAAX,EAAyB5C,MAAM,CAAC4C,YAAP,GALM,CAO/B;;AACA,MAAI5C,MAAM,CAACyC,SAAP,IAAoBzC,MAAM,CAAC6C,cAA/B,EAA+CtE,MAAM,CAACuE,GAAP,CAAWC,eAAX,CAA2B/C,MAAM,CAACgD,SAAlC,EARhB,CAU/B;;AACA,MAAIC,KAAK,GAAG,WAAZ;;AAEA,MAAIzE,gDAAO,CAACW,QAAR,MAAsBX,gDAAO,CAACC,SAAR,EAA1B,EAA+C;AAC7C;AACAwE,SAAK,GAAG,OAAR;AACD;;AAED,MAAMC,OAAO,GAAG,SAAVA,OAAU,GAAM;AACpB;AACA3E,UAAM,CAAC4E,mBAAP,CAA2BF,KAA3B,EAAkCC,OAAlC;AAEAlD,UAAM,CAACoD,kBAAP,GAJoB,CAMpB;;AACA,QAAMC,MAAM,GAAGtE,QAAQ,CAACuE,cAAT,CAAwBtD,MAAM,CAACuD,OAA/B,CAAf;;AAEA,QAAIF,MAAJ,EAAY;AACVA,YAAM,CAACG,MAAP;AACD;AACF,GAZD;;AAcAjF,QAAM,CAACkF,gBAAP,CAAwBR,KAAxB,EAA+BC,OAA/B;AACD;AAEM,SAASrB,SAAT,CAAoB6B,GAApB,EAAyB;AAC9B,MAAMC,SAAS,GAAG,IAAIC,MAAJ,CAAW,6CAAX,CAAlB;AACA,SAAOD,SAAS,CAACE,IAAV,CAAeH,GAAf,CAAP;AACD,C;;;;;;;;;;;;;;;;;ACtGD;AACA;AAEe;AACbrF,OAAK,EAAE,eAAC2B,MAAD,EAAS8D,UAAT,EAAwB;AAC7B;AACA,QAAMpC,YAAY,GAAGqC,aAAa,CAAC/D,MAAM,CAACgD,SAAR,CAAb,GAAkChD,MAAM,CAACgD,SAAzC,GAAqDjE,QAAQ,CAACuE,cAAT,CAAwBtD,MAAM,CAACgD,SAA/B,CAA1E,CAF6B,CAI7B;;AACA,QAAI,CAACtB,YAAL,EAAmB;AACjBnD,YAAM,CAACyF,OAAP,CAAeC,KAAf,CAAqB,8BAA8BjE,MAAM,CAACgD,SAA1D;AACA;AACD,KAR4B,CAU7B;;;AACAhD,UAAM,CAACkE,gBAAP,GAA0BC,YAAY,CAACzC,YAAD,EAAe1B,MAAf,CAAtC,CAX6B,CAa7B;;AACA,QAAIA,MAAM,CAAC8B,MAAX,EAAmB;AACjBL,kEAAS,CAACzB,MAAM,CAACkE,gBAAR,EAA0BlE,MAA1B,CAAT;AACD,KAhB4B,CAkB7B;;;AACAoE,kDAAK,CAACC,IAAN,CAAWrE,MAAX,EAAmB8D,UAAnB;AACD;AArBY,CAAf;;AAwBA,SAASK,YAAT,CAAuBzD,OAAvB,EAAgCV,MAAhC,EAAwC;AACtC;AACA,MAAMsE,KAAK,GAAG5D,OAAO,CAAC6D,SAAR,EAAd,CAFsC,CAItC;;AACA,MAAMC,eAAe,GAAGC,KAAK,CAACjF,SAAN,CAAgBgB,KAAhB,CAAsBd,IAAtB,CAA2BgB,OAAO,CAAC6B,UAAnC,CAAxB;;AACA,OAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgD,eAAe,CAACvD,MAApC,EAA4CO,CAAC,EAA7C,EAAiD;AAC/C;AACA,QAAIxB,MAAM,CAAC0E,cAAP,CAAsB5F,OAAtB,CAA8B0F,eAAe,CAAChD,CAAD,CAAf,CAAmBmD,EAAjD,MAAyD,CAAC,CAA9D,EAAiE;AAC/D;AACD,KAJ8C,CAM/C;;;AACA,QAAMC,WAAW,GAAGT,YAAY,CAACK,eAAe,CAAChD,CAAD,CAAhB,EAAqBxB,MAArB,CAAhC,CAP+C,CAS/C;;AACAsE,SAAK,CAACnC,WAAN,CAAkByC,WAAlB;AACD,GAjBqC,CAmBtC;;;AACA,MAAI5E,MAAM,CAAC6E,UAAP,IAAqBnE,OAAO,CAACoE,QAAR,KAAqB,CAA9C,EAAiD;AAC/CR,SAAK,CAAClC,YAAN,CAAmB,OAAnB,EAA4B3B,gEAAa,CAACC,OAAD,EAAUV,MAAV,CAAzC;AACD,GAtBqC,CAwBtC;;;AACA,UAAQU,OAAO,CAACqE,OAAhB;AACE,SAAK,QAAL;AACE;AACAT,WAAK,CAAC/C,KAAN,GAAcb,OAAO,CAACa,KAAtB;AACA;;AACF,SAAK,QAAL;AACE;AACA+C,WAAK,CAACU,UAAN,CAAiB,IAAjB,EAAuBC,SAAvB,CAAiCvE,OAAjC,EAA0C,CAA1C,EAA6C,CAA7C;AACA;AARJ;;AAWA,SAAO4D,KAAP;AACD;;AAED,SAASP,aAAT,CAAwBf,SAAxB,EAAmC;AACjC;AACA,SAAO,QAAOA,SAAP,MAAqB,QAArB,IAAiCA,SAAjC,KAA+CA,SAAS,YAAYrD,WAArB,IAAoCqD,SAAS,CAAC8B,QAAV,KAAuB,CAA1G,CAAP;AACD,C;;;;;;;;;;;;ACrED;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEe;AACbzG,OAAK,EAAE,eAAC2B,MAAD,EAAS8D,UAAT,EAAwB;AAC7B;AACA,QAAI9D,MAAM,CAACgD,SAAP,CAAiBkC,WAAjB,KAAiCT,KAArC,EAA4C;AAC1C;AACAzE,YAAM,CAACgD,SAAP,GAAmB,CAAChD,MAAM,CAACgD,SAAR,CAAnB;AACD,KAL4B,CAO7B;;;AACAhD,UAAM,CAACkE,gBAAP,GAA0BnF,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAA1B,CAR6B,CAU7B;;AACA5B,UAAM,CAACgD,SAAP,CAAiBmC,OAAjB,CAAyB,UAAAC,GAAG,EAAI;AAC9B;AACA,UAAMC,GAAG,GAAGtG,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAAZ;AACAyD,SAAG,CAACjD,YAAJ,CAAiB,OAAjB,EAA0BpC,MAAM,CAACsF,UAAjC,EAH8B,CAK9B;;AACAD,SAAG,CAACD,GAAJ,GAAUA,GAAV,CAN8B,CAQ9B;AACA;;AACA,UAAI5G,gDAAO,CAACC,SAAR,EAAJ,EAAyB;AACvB,YAAM8G,iBAAiB,GAAGF,GAAG,CAACD,GAA9B;AACAC,WAAG,CAACD,GAAJ,GAAUG,iBAAV;AACD,OAb6B,CAe9B;;;AACA,UAAMC,YAAY,GAAGzG,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAArB,CAhB8B,CAkB9B;;AACA4D,kBAAY,CAACrD,WAAb,CAAyBkD,GAAzB,EAnB8B,CAqB9B;;AACArF,YAAM,CAACkE,gBAAP,CAAwB/B,WAAxB,CAAoCqD,YAApC;AACD,KAvBD,EAX6B,CAoC7B;;AACA,QAAIxF,MAAM,CAAC8B,MAAX,EAAmBL,4DAAS,CAACzB,MAAM,CAACkE,gBAAR,EAA0BlE,MAA1B,CAAT,CArCU,CAuC7B;;AACAoE,kDAAK,CAACC,IAAN,CAAWrE,MAAX,EAAmB8D,UAAnB;AACD;AA1CY,CAAf,E;;;;;;;;;;;;ACJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAM2B,UAAU,GAAG,CAAC,KAAD,EAAQ,MAAR,EAAgB,OAAhB,EAAyB,MAAzB,EAAiC,UAAjC,CAAnB;AAEe;AACbnH,MADa,kBACL;AACN,QAAM0B,MAAM,GAAG;AACbgD,eAAS,EAAE,IADE;AAEb0C,uBAAiB,EAAE,IAFN;AAGbC,UAAI,EAAE,KAHO;AAIb7D,YAAM,EAAE,IAJK;AAKbO,iBAAW,EAAE,mBALA;AAMbf,cAAQ,EAAE,GANG;AAObsE,gBAAU,EAAE,IAPC;AAQbC,qBAAe,EAAE,6DARJ;AASbC,eAAS,EAAE,mDATE;AAUbrD,eAAS,EAAE,KAVE;AAWbsD,aAAO,EAAE,iBAAC9B,KAAD,EAAW;AAAE,cAAMA,KAAN;AAAa,OAXtB;AAYbpB,oBAAc,EAAE,IAZH;AAabD,kBAAY,EAAE,IAbD;AAcbQ,wBAAkB,EAAE,8BAAM,CAAE,CAdf;AAeb4C,2BAAqB,EAAE,iCAAM,CAAE,CAflB;AAgBbC,kBAAY,EAAE,wBAhBD;AAiBb1C,aAAO,EAAE,SAjBI;AAkBbW,sBAAgB,EAAE,IAlBL;AAmBbgC,mBAAa,EAAE,UAnBF;AAoBb/E,iBAAW,EAAE,CAAC,OAAD,EAAU,SAAV,EAAqB,OAArB,EAA8B,WAA9B,EAA2C,QAA3C,EAAqD,YAArD,EAAmE,YAAnE,CApBA;AAqBbD,kBAAY,EAAE,CAAC,QAAD,EAAW,KAAX,EAAkB,OAAlB,EAA2B,iBAA3B,CArBD;AAsBbwD,oBAAc,EAAE,EAtBH;AAuBbyB,uBAAiB,EAAE,IAvBN;AAwBbC,SAAG,EAAE,IAxBQ;AAyBbC,WAAK,EAAE,IAzBM;AA0BbxB,gBAAU,EAAE,IA1BC;AA2BbyB,YAAM,EAAE,KA3BK;AA6Bb;AACAC,eAAS,EAAE,IA9BE;AA+BbrG,UAAI,EAAE,eA/BO;AAgCbC,eAAS,EAAE,MAhCE;AAiCbqG,wBAAkB,EAAE,IAjCP;AAkCbC,gBAAU,EAAE,KAlCC;AAmCbnB,gBAAU,EAAE;AAnCC,KAAf,CADM,CAuCN;;AACA,QAAMoB,IAAI,GAAGC,SAAS,CAAC,CAAD,CAAtB;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACtB,YAAM,IAAIC,KAAJ,CAAU,uCAAV,CAAN;AACD,KA3CK,CA6CN;;;AACA,oBAAeH,IAAf;AACE,WAAK,QAAL;AACE1G,cAAM,CAACgD,SAAP,GAAmB8D,SAAS,CAACJ,IAAD,CAA5B;AACA1G,cAAM,CAAC0F,iBAAP,GAA2B1F,MAAM,CAACgD,SAAlC;AACAhD,cAAM,CAAC2F,IAAP,GAAcgB,SAAS,CAAC,CAAD,CAAT,IAAgB3G,MAAM,CAAC2F,IAArC;AACA;;AACF,WAAK,QAAL;AACE3F,cAAM,CAACgD,SAAP,GAAmB0D,IAAI,CAAC1D,SAAxB;AACAhD,cAAM,CAAC0F,iBAAP,GAA2B,OAAOgB,IAAI,CAAChB,iBAAZ,KAAkC,WAAlC,GAAgDgB,IAAI,CAAChB,iBAArD,GAAyE1F,MAAM,CAACgD,SAA3G;AACAhD,cAAM,CAAC0F,iBAAP,GAA2B1F,MAAM,CAACsG,MAAP,yCAA+CtG,MAAM,CAAC0F,iBAAtD,IAA4E1F,MAAM,CAAC0F,iBAA9G;;AACA,aAAK,IAAIqB,CAAT,IAAc/G,MAAd,EAAsB;AACpB,cAAI+G,CAAC,KAAK,WAAN,IAAqBA,CAAC,KAAK,mBAA/B,EAAoD;AAEpD/G,gBAAM,CAAC+G,CAAD,CAAN,GAAY,OAAOL,IAAI,CAACK,CAAD,CAAX,KAAmB,WAAnB,GAAiCL,IAAI,CAACK,CAAD,CAArC,GAA2C/G,MAAM,CAAC+G,CAAD,CAA7D;AACD;;AACD;;AACF;AACE,cAAM,IAAIF,KAAJ,CAAU,0EAAyEH,IAAzE,CAAV,CAAN;AAjBJ,KA9CM,CAkEN;;;AACA,QAAI,CAAC1G,MAAM,CAACgD,SAAZ,EAAuB,MAAM,IAAI6D,KAAJ,CAAU,gCAAV,CAAN,CAnEjB,CAqEN;;AACA,QAAI,CAAC7G,MAAM,CAAC2F,IAAR,IAAgB,OAAO3F,MAAM,CAAC2F,IAAd,KAAuB,QAAvC,IAAmDF,UAAU,CAAC3G,OAAX,CAAmBkB,MAAM,CAAC2F,IAAP,CAAY/F,WAAZ,EAAnB,MAAkD,CAAC,CAA1G,EAA6G;AAC3G,YAAM,IAAIiH,KAAJ,CAAU,qEAAV,CAAN;AACD,KAxEK,CA0EN;;;AACA,QAAI7G,MAAM,CAACyC,SAAX,EAAsBC,8CAAK,CAACsE,IAAN,CAAWhH,MAAX,EA3EhB,CA6EN;;AACA,QAAIA,MAAM,CAAC6C,cAAX,EAA2B7C,MAAM,CAAC6C,cAAP,GA9ErB,CAgFN;;AACA,QAAMoE,SAAS,GAAGlI,QAAQ,CAACuE,cAAT,CAAwBtD,MAAM,CAACuD,OAA/B,CAAlB;AAEA,QAAI0D,SAAJ,EAAeA,SAAS,CAACC,UAAV,CAAqBC,WAArB,CAAiCF,SAAjC,EAnFT,CAqFN;;AACA,QAAMnD,UAAU,GAAG/E,QAAQ,CAAC6C,aAAT,CAAuB,QAAvB,CAAnB;;AAEA,QAAIpD,gDAAO,CAACC,SAAR,EAAJ,EAAyB;AACvB;AACA;AACA;AACAqF,gBAAU,CAAC1B,YAAX,CAAwB,OAAxB,EAAiC,iHAAjC;AACD,KALD,MAKO;AACL;AACA0B,gBAAU,CAAC1B,YAAX,CAAwB,OAAxB,EAAiC,wEAAjC;AACD,KAhGK,CAkGN;;;AACA0B,cAAU,CAAC1B,YAAX,CAAwB,IAAxB,EAA8BpC,MAAM,CAACuD,OAArC,EAnGM,CAqGN;;AACA,QAAIvD,MAAM,CAAC2F,IAAP,KAAgB,KAApB,EAA2B;AACzB7B,gBAAU,CAACsD,MAAX,GAAoB,wBAAwBpH,MAAM,CAACkG,aAA/B,GAA+C,UAAnE,CADyB,CAGzB;;AACA,UAAIlG,MAAM,CAACoG,GAAX,EAAgB;AACd;AACA,YAAI,CAAC3B,KAAK,CAAC4C,OAAN,CAAcrH,MAAM,CAACoG,GAArB,CAAL,EAAgCpG,MAAM,CAACoG,GAAP,GAAa,CAACpG,MAAM,CAACoG,GAAR,CAAb,CAFlB,CAId;;AACApG,cAAM,CAACoG,GAAP,CAAWjB,OAAX,CAAmB,UAAAmC,IAAI,EAAI;AACzBxD,oBAAU,CAACsD,MAAX,IAAqB,kCAAkCE,IAAlC,GAAyC,IAA9D;AACD,SAFD;AAGD;;AAEDxD,gBAAU,CAACsD,MAAX,IAAqB,6BAArB;AACD,KArHK,CAuHN;;;AACA,YAAQpH,MAAM,CAAC2F,IAAf;AACE,WAAK,KAAL;AACE;AACA,YAAInH,gDAAO,CAACG,IAAR,EAAJ,EAAoB;AAClB,cAAI;AACFqF,mBAAO,CAACuD,IAAR,CAAa,8DAAb;AACA,gBAAM5G,GAAG,GAAGpC,MAAM,CAACiJ,IAAP,CAAYxH,MAAM,CAAC0F,iBAAnB,EAAsC,QAAtC,CAAZ;AACA/E,eAAG,CAAC8G,KAAJ;AACAzH,kBAAM,CAACgG,qBAAP;AACD,WALD,CAKE,OAAO/B,KAAP,EAAc;AACdjE,kBAAM,CAAC+F,OAAP,CAAe9B,KAAf;AACD,WAPD,SAOU;AACR;AACA,gBAAIjE,MAAM,CAACyC,SAAX,EAAsBC,8CAAK,CAACC,KAAN;AACtB,gBAAI3C,MAAM,CAAC4C,YAAX,EAAyB5C,MAAM,CAAC4C,YAAP;AAC1B;AACF,SAbD,MAaO;AACL8E,sDAAG,CAACrJ,KAAJ,CAAU2B,MAAV,EAAkB8D,UAAlB;AACD;;AACD;;AACF,WAAK,OAAL;AACE6D,sDAAK,CAACtJ,KAAN,CAAY2B,MAAZ,EAAoB8D,UAApB;AACA;;AACF,WAAK,MAAL;AACE8D,qDAAI,CAACvJ,KAAL,CAAW2B,MAAX,EAAmB8D,UAAnB;AACA;;AACF,WAAK,UAAL;AACE+D,yDAAO,CAACxJ,KAAR,CAAc2B,MAAd,EAAsB8D,UAAtB;AACA;;AACF,WAAK,MAAL;AACEgE,qDAAI,CAACzJ,KAAL,CAAW2B,MAAX,EAAmB8D,UAAnB;AACA;AA/BJ;AAiCD;AA1JY,CAAf,E;;;;;;;;;;;;;;;;;ACZA;AACA;AAEe;AACbzF,OAAK,EAAE,eAAC2B,MAAD,EAAS8D,UAAT,EAAwB;AAC7B;AACA,QAAI,QAAO9D,MAAM,CAACgD,SAAd,MAA4B,QAAhC,EAA0C;AACxC,YAAM,IAAI6D,KAAJ,CAAU,wCAAV,CAAN;AACD,KAJ4B,CAM7B;;;AACA,QAAI,OAAO7G,MAAM,CAACmG,iBAAd,KAAoC,SAAxC,EAAmD;AACjD,YAAM,IAAIU,KAAJ,CAAU,uDAAV,CAAN;AACD,KAT4B,CAW7B;;;AACA,QAAI,CAAC7G,MAAM,CAAC4F,UAAR,IAAsB,CAACnB,KAAK,CAAC4C,OAAN,CAAcrH,MAAM,CAAC4F,UAArB,CAA3B,EAA6D;AAC3D,YAAM,IAAIiB,KAAJ,CAAU,8CAAV,CAAN;AACD,KAd4B,CAgB7B;;;AACA7G,UAAM,CAAC4F,UAAP,GAAoB5F,MAAM,CAAC4F,UAAP,CAAkBmC,GAAlB,CAAsB,UAAAC,QAAQ,EAAI;AACpD,aAAO;AACLC,aAAK,EAAE,QAAOD,QAAP,MAAoB,QAApB,GAA+BA,QAAQ,CAACC,KAAxC,GAAgDD,QADlD;AAELE,mBAAW,EAAE,QAAOF,QAAP,MAAoB,QAApB,GAA+BA,QAAQ,CAACE,WAAxC,GAAsDF,QAF9D;AAGLG,kBAAU,EAAE,QAAOH,QAAP,MAAoB,QAApB,IAAgCA,QAAQ,CAACG,UAAzC,GAAsDH,QAAQ,CAACG,UAAT,GAAsB,GAA5E,GAAkF,MAAMnI,MAAM,CAAC4F,UAAP,CAAkB3E,MAAxB,GAAiC;AAH1H,OAAP;AAKD,KANmB,CAApB,CAjB6B,CAyB7B;;AACAjB,UAAM,CAACkE,gBAAP,GAA0BnF,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAA1B,CA1B6B,CA4B7B;;AACA,QAAI5B,MAAM,CAAC8B,MAAX,EAAmB;AACjBL,kEAAS,CAACzB,MAAM,CAACkE,gBAAR,EAA0BlE,MAA1B,CAAT;AACD,KA/B4B,CAiC7B;;;AACAA,UAAM,CAACkE,gBAAP,CAAwBnC,SAAxB,IAAqCqG,UAAU,CAACpI,MAAD,CAA/C,CAlC6B,CAoC7B;;AACAoE,kDAAK,CAACC,IAAN,CAAWrE,MAAX,EAAmB8D,UAAnB;AACD;AAvCY,CAAf;;AA0CA,SAASsE,UAAT,CAAqBpI,MAArB,EAA6B;AAC3B;AACA,MAAMqI,IAAI,GAAGrI,MAAM,CAACgD,SAApB;AACA,MAAM4C,UAAU,GAAG5F,MAAM,CAAC4F,UAA1B,CAH2B,CAK3B;;AACA,MAAI7F,QAAQ,GAAG,yDAAf,CAN2B,CAQ3B;;AACA,MAAIC,MAAM,CAACmG,iBAAX,EAA8B;AAC5BpG,YAAQ,IAAI,SAAZ;AACD,GAX0B,CAa3B;;;AACAA,UAAQ,IAAI,MAAZ,CAd2B,CAgB3B;;AACA,OAAK,IAAIuI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1C,UAAU,CAAC3E,MAA/B,EAAuCqH,CAAC,EAAxC,EAA4C;AAC1CvI,YAAQ,IAAI,sBAAsB6F,UAAU,CAAC0C,CAAD,CAAV,CAAcH,UAApC,GAAiD,GAAjD,GAAuDnI,MAAM,CAAC6F,eAA9D,GAAgF,IAAhF,GAAuFzF,kEAAe,CAACwF,UAAU,CAAC0C,CAAD,CAAV,CAAcJ,WAAf,CAAtG,GAAoI,OAAhJ;AACD,GAnB0B,CAqB3B;;;AACAnI,UAAQ,IAAI,OAAZ,CAtB2B,CAwB3B;;AACA,MAAIC,MAAM,CAACmG,iBAAX,EAA8B;AAC5BpG,YAAQ,IAAI,UAAZ;AACD,GA3B0B,CA6B3B;;;AACAA,UAAQ,IAAI,SAAZ,CA9B2B,CAgC3B;;AACA,OAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6G,IAAI,CAACpH,MAAzB,EAAiCO,CAAC,EAAlC,EAAsC;AACpC;AACAzB,YAAQ,IAAI,MAAZ,CAFoC,CAIpC;;AACA,SAAK,IAAIwI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG3C,UAAU,CAAC3E,MAA/B,EAAuCsH,CAAC,EAAxC,EAA4C;AAC1C,UAAIC,UAAU,GAAGH,IAAI,CAAC7G,CAAD,CAArB,CAD0C,CAG1C;;AACA,UAAMwG,QAAQ,GAAGpC,UAAU,CAAC2C,CAAD,CAAV,CAAcN,KAAd,CAAoBQ,KAApB,CAA0B,GAA1B,CAAjB;;AACA,UAAIT,QAAQ,CAAC/G,MAAT,GAAkB,CAAtB,EAAyB;AACvB,aAAK,IAAIyH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,QAAQ,CAAC/G,MAA7B,EAAqCyH,CAAC,EAAtC,EAA0C;AACxCF,oBAAU,GAAGA,UAAU,CAACR,QAAQ,CAACU,CAAD,CAAT,CAAvB;AACD;AACF,OAJD,MAIO;AACLF,kBAAU,GAAGA,UAAU,CAAC5C,UAAU,CAAC2C,CAAD,CAAV,CAAcN,KAAf,CAAvB;AACD,OAXyC,CAa1C;;;AACAlI,cAAQ,IAAI,sBAAsB6F,UAAU,CAAC2C,CAAD,CAAV,CAAcJ,UAApC,GAAiDnI,MAAM,CAAC8F,SAAxD,GAAoE,IAApE,GAA2E0C,UAA3E,GAAwF,OAApG;AACD,KApBmC,CAsBpC;;;AACAzI,YAAQ,IAAI,OAAZ;AACD,GAzD0B,CA2D3B;;;AACAA,UAAQ,IAAI,kBAAZ;AAEA,SAAOA,QAAP;AACD,C;;;;;;;;;;;;AC5GD;AAAA,IAAM2C,KAAK,GAAG;AACZsE,MADY,gBACNhH,MADM,EACE;AACZ;AACA,QAAM2I,UAAU,GAAG,6BACf,iBADe,GAEf,qBAFe,GAGf,mBAHe,GAIf,kBAJe,GAKf,gBALe,GAMf,kBANe,GAOf,gBAPe,GAQf,kBARe,GASf,eATe,GAUf,gBAVe,GAWf,wCAXe,GAYf,+BAZJ,CAFY,CAgBZ;;AACA,QAAMC,UAAU,GAAG7J,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAAnB;AACAgH,cAAU,CAACxG,YAAX,CAAwB,OAAxB,EAAiCuG,UAAjC;AACAC,cAAU,CAACxG,YAAX,CAAwB,IAAxB,EAA8B,eAA9B,EAnBY,CAqBZ;;AACA,QAAMyG,UAAU,GAAG9J,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAAnB;AACAiH,cAAU,CAACzG,YAAX,CAAwB,OAAxB,EAAiC,kEAAjC,EAvBY,CAyBZ;;AACA,QAAM0G,WAAW,GAAG/J,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAApB;AACAkH,eAAW,CAAC1G,YAAZ,CAAyB,OAAzB,EAAkC,YAAlC;AACA0G,eAAW,CAAC1G,YAAZ,CAAyB,IAAzB,EAA+B,YAA/B;AACAyG,cAAU,CAAC1G,WAAX,CAAuB2G,WAAvB,EA7BY,CA+BZ;;AACA,QAAMC,OAAO,GAAGhK,QAAQ,CAAC6C,aAAT,CAAuB,MAAvB,CAAhB;AACAmH,WAAO,CAAC3G,YAAR,CAAqB,OAArB,EAA8B,cAA9B;AACAyG,cAAU,CAAC1G,WAAX,CAAuB4G,OAAvB,EAlCY,CAoCZ;;AACA,QAAMC,WAAW,GAAGjK,QAAQ,CAACmD,cAAT,CAAwBlC,MAAM,CAACiG,YAA/B,CAApB;AACA4C,cAAU,CAAC1G,WAAX,CAAuB6G,WAAvB,EAtCY,CAwCZ;;AACAJ,cAAU,CAACzG,WAAX,CAAuB0G,UAAvB,EAzCY,CA2CZ;;AACA9J,YAAQ,CAACkK,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyC9G,WAAzC,CAAqDyG,UAArD,EA5CY,CA8CZ;;AACA7J,YAAQ,CAACuE,cAAT,CAAwB,YAAxB,EAAsCG,gBAAtC,CAAuD,OAAvD,EAAgE,YAAY;AAC1Ef,WAAK,CAACC,KAAN;AACD,KAFD;AAGD,GAnDW;AAoDZA,OApDY,mBAoDH;AACP,QAAMiG,UAAU,GAAG7J,QAAQ,CAACuE,cAAT,CAAwB,eAAxB,CAAnB;;AAEA,QAAIsF,UAAJ,EAAgB;AACdA,gBAAU,CAAC1B,UAAX,CAAsBC,WAAtB,CAAkCyB,UAAlC;AACD;AACF;AA1DW,CAAd;AA6DelG,oEAAf,E;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AACA;AAEe;AACbrE,OAAK,EAAE,eAAC2B,MAAD,EAAS8D,UAAT,EAAwB;AAC7B;AACA,QAAI9D,MAAM,CAACsG,MAAX,EAAmB;AACjB,UAAM4C,UAAU,GAAGC,UAAU,CAACC,IAAX,CAAgBC,IAAI,CAACrJ,MAAM,CAACgD,SAAR,CAApB,EAAwC,UAAAsG,CAAC;AAAA,eAAIA,CAAC,CAACC,UAAF,CAAa,CAAb,CAAJ;AAAA,OAAzC,CAAnB;AACAC,wBAAkB,CAACxJ,MAAD,EAAS8D,UAAT,EAAqBoF,UAArB,CAAlB;AACA;AACD,KAN4B,CAQ7B;;;AACAlJ,UAAM,CAACgD,SAAP,GAAmB,qBAAqBa,IAArB,CAA0B7D,MAAM,CAACgD,SAAjC,IACfhD,MAAM,CAACgD,SADQ,GAEfzE,MAAM,CAACkL,QAAP,CAAgBC,MAAhB,IAA0B1J,MAAM,CAACgD,SAAP,CAAiB1C,MAAjB,CAAwB,CAAxB,MAA+B,GAA/B,GAAqC,MAAMN,MAAM,CAACgD,SAAlD,GAA8DhD,MAAM,CAACgD,SAA/F,CAFJ,CAT6B,CAa7B;;AACA,QAAM2G,GAAG,GAAG,IAAIpL,MAAM,CAACqL,cAAX,EAAZ;AACAD,OAAG,CAACE,YAAJ,GAAmB,aAAnB;AAEAF,OAAG,CAAClG,gBAAJ,CAAqB,OAArB,EAA8B,YAAM;AAClCjB,gEAAO,CAACxC,MAAD,CAAP;AACAA,YAAM,CAAC+F,OAAP,CAAe4D,GAAG,CAACG,UAAnB,EAA+BH,GAA/B,EAFkC,CAIlC;AACD,KALD;AAOAA,OAAG,CAAClG,gBAAJ,CAAqB,MAArB,EAA6B,YAAM;AACjC;AACA,UAAI,CAAC,GAAD,EAAM,GAAN,EAAW3E,OAAX,CAAmB6K,GAAG,CAACI,MAAvB,MAAmC,CAAC,CAAxC,EAA2C;AACzCvH,kEAAO,CAACxC,MAAD,CAAP;AACAA,cAAM,CAAC+F,OAAP,CAAe4D,GAAG,CAACG,UAAnB,EAA+BH,GAA/B,EAFyC,CAIzC;;AACA;AACD,OARgC,CAUjC;;;AACAH,wBAAkB,CAACxJ,MAAD,EAAS8D,UAAT,EAAqB6F,GAAG,CAACK,QAAzB,CAAlB;AACD,KAZD;AAcAL,OAAG,CAACnC,IAAJ,CAAS,KAAT,EAAgBxH,MAAM,CAACgD,SAAvB,EAAkC,IAAlC;AACA2G,OAAG,CAACtF,IAAJ;AACD;AAzCY,CAAf;;AA4CA,SAASmF,kBAAT,CAA6BxJ,MAA7B,EAAqC8D,UAArC,EAAiDuE,IAAjD,EAAuD;AACrD;AACA,MAAI4B,QAAQ,GAAG,IAAI1L,MAAM,CAAC2L,IAAX,CAAgB,CAAC7B,IAAD,CAAhB,EAAwB;AAAE1C,QAAI,EAAE;AAAR,GAAxB,CAAf;AACAsE,UAAQ,GAAG1L,MAAM,CAACuE,GAAP,CAAWqH,eAAX,CAA2BF,QAA3B,CAAX,CAHqD,CAKrD;;AACAnG,YAAU,CAAC1B,YAAX,CAAwB,KAAxB,EAA+B6H,QAA/B;AAEA7F,gDAAK,CAACC,IAAN,CAAWrE,MAAX,EAAmB8D,UAAnB;AACD,C;;;;;;;;;;;;ACxDD;AAAA;AAAA;AAAA;AACA;AAEA,IAAMM,KAAK,GAAG;AACZC,MAAI,EAAE,cAACrE,MAAD,EAAS8D,UAAT,EAAwB;AAC5B;AACA/E,YAAQ,CAACkK,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyC9G,WAAzC,CAAqD2B,UAArD,EAF4B,CAI5B;;AACA,QAAMsG,aAAa,GAAGrL,QAAQ,CAACuE,cAAT,CAAwBtD,MAAM,CAACuD,OAA/B,CAAtB,CAL4B,CAO5B;;AACA6G,iBAAa,CAACC,MAAd,GAAuB,YAAM;AAC3B,UAAIrK,MAAM,CAAC2F,IAAP,KAAgB,KAApB,EAA2B;AACzB;AACA,YAAInH,gDAAO,CAACC,SAAR,EAAJ,EAAyB;AACvB6L,oBAAU,CAAC;AAAA,mBAAMC,YAAY,CAACH,aAAD,EAAgBpK,MAAhB,CAAlB;AAAA,WAAD,EAA4C,IAA5C,CAAV;AACD,SAFD,MAEO;AACLuK,sBAAY,CAACH,aAAD,EAAgBpK,MAAhB,CAAZ;AACD;;AACD;AACD,OAT0B,CAW3B;;;AACA,UAAIwK,aAAa,GAAIJ,aAAa,CAACK,aAAd,IAA+BL,aAAa,CAACM,eAAlE;AACA,UAAIF,aAAa,CAACzL,QAAlB,EAA4ByL,aAAa,GAAGA,aAAa,CAACzL,QAA9B,CAbD,CAe3B;;AACAyL,mBAAa,CAACG,IAAd,CAAmBxI,WAAnB,CAA+BnC,MAAM,CAACkE,gBAAtC,EAhB2B,CAkB3B;;AACA,UAAIlE,MAAM,CAAC2F,IAAP,KAAgB,KAAhB,IAAyB3F,MAAM,CAACqG,KAApC,EAA2C;AACzC;AACA,YAAMA,KAAK,GAAGtH,QAAQ,CAAC6C,aAAT,CAAuB,OAAvB,CAAd;AACAyE,aAAK,CAACtE,SAAN,GAAkB/B,MAAM,CAACqG,KAAzB,CAHyC,CAKzC;;AACAmE,qBAAa,CAACI,IAAd,CAAmBzI,WAAnB,CAA+BkE,KAA/B;AACD,OA1B0B,CA4B3B;;;AACA,UAAMwE,MAAM,GAAGL,aAAa,CAACvB,oBAAd,CAAmC,KAAnC,CAAf;;AAEA,UAAI4B,MAAM,CAAC5J,MAAP,GAAgB,CAApB,EAAuB;AACrB6J,wBAAgB,CAACrG,KAAK,CAAC2E,IAAN,CAAWyB,MAAX,CAAD,CAAhB,CAAqCE,IAArC,CAA0C;AAAA,iBAAMR,YAAY,CAACH,aAAD,EAAgBpK,MAAhB,CAAlB;AAAA,SAA1C;AACD,OAFD,MAEO;AACLuK,oBAAY,CAACH,aAAD,EAAgBpK,MAAhB,CAAZ;AACD;AACF,KApCD;AAqCD;AA9CW,CAAd;;AAiDA,SAASuK,YAAT,CAAuBH,aAAvB,EAAsCpK,MAAtC,EAA8C;AAC5C,MAAI;AACFoK,iBAAa,CAAC3C,KAAd,GADE,CAGF;;AACA,QAAIjJ,gDAAO,CAACS,MAAR,MAAoBT,gDAAO,CAACG,IAAR,EAAxB,EAAwC;AACtC,UAAI;AACFyL,qBAAa,CAACK,aAAd,CAA4B1L,QAA5B,CAAqCiM,WAArC,CAAiD,OAAjD,EAA0D,KAA1D,EAAiE,IAAjE;AACD,OAFD,CAEE,OAAOC,CAAP,EAAU;AACVb,qBAAa,CAACK,aAAd,CAA4BpM,KAA5B;AACD;AACF,KAND,MAMO;AACL;AACA+L,mBAAa,CAACK,aAAd,CAA4BpM,KAA5B;AACD;AACF,GAdD,CAcE,OAAO4F,KAAP,EAAc;AACdjE,UAAM,CAAC+F,OAAP,CAAe9B,KAAf;AACD,GAhBD,SAgBU;AACR,QAAIzF,gDAAO,CAACC,SAAR,EAAJ,EAAyB;AACvB;AACA2L,mBAAa,CAAC/D,KAAd,CAAoB6E,UAApB,GAAiC,QAAjC;AACAd,mBAAa,CAAC/D,KAAd,CAAoB8E,IAApB,GAA2B,MAA3B;AACD;;AAED3I,8DAAO,CAACxC,MAAD,CAAP;AACD;AACF;;AAED,SAAS8K,gBAAT,CAA2BD,MAA3B,EAAmC;AACjC,MAAMO,QAAQ,GAAGP,MAAM,CAAC9C,GAAP,CAAW,UAAAsD,KAAK,EAAI;AACnC,QAAIA,KAAK,CAACjG,GAAN,IAAaiG,KAAK,CAACjG,GAAN,KAAc7G,MAAM,CAACkL,QAAP,CAAgB6B,IAA/C,EAAqD;AACnD,aAAOC,eAAe,CAACF,KAAD,CAAtB;AACD;AACF,GAJgB,CAAjB;AAMA,SAAOG,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAP;AACD;;AAED,SAASG,eAAT,CAA0BF,KAA1B,EAAiC;AAC/B,SAAO,IAAIG,OAAJ,CAAY,UAAAE,OAAO,EAAI;AAC5B,QAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;AACtB,OAACN,KAAD,IAAU,OAAOA,KAAK,CAACO,YAAb,KAA8B,WAAxC,IAAuDP,KAAK,CAACO,YAAN,KAAuB,CAA9E,IAAmF,CAACP,KAAK,CAACQ,QAA1F,GACIvB,UAAU,CAACqB,SAAD,EAAY,GAAZ,CADd,GAEID,OAAO,EAFX;AAGD,KAJD;;AAKAC,aAAS;AACV,GAPM,CAAP;AAQD;;AAEcvH,oEAAf,E;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAEe;AACb/F,OAAK,EAAE,eAAC2B,MAAD,EAAS8D,UAAT,EAAwB;AAC7B;AACA9D,UAAM,CAACkE,gBAAP,GAA0BnF,QAAQ,CAAC6C,aAAT,CAAuB,KAAvB,CAA1B;AACA5B,UAAM,CAACkE,gBAAP,CAAwB9B,YAAxB,CAAqC,OAArC,EAA8C,YAA9C,EAH6B,CAK7B;;AACApC,UAAM,CAACkE,gBAAP,CAAwBnC,SAAxB,GAAoC/B,MAAM,CAACgD,SAA3C,CAN6B,CAQ7B;;AACAoB,kDAAK,CAACC,IAAN,CAAWrE,MAAX,EAAmB8D,UAAnB;AACD;AAXY,CAAf,E;;;;;;;;;;;ACFA,uC", "file": "print.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"printJS\"] = factory();\n\telse\n\t\troot[\"printJS\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import './sass/index.scss'\nimport print from './js/init'\n\nconst printJS = print.init\n\nif (typeof window !== 'undefined') {\n  window.printJS = printJS\n}\n\nexport default printJS\n", "const Browser = {\n  // Firefox 1.0+\n  isFirefox: () => {\n    return typeof InstallTrigger !== 'undefined'\n  },\n  // Internet Explorer 6-11\n  isIE: () => {\n    return navigator.userAgent.indexOf('MSIE') !== -1 || !!document.documentMode\n  },\n  // Edge 20+\n  isEdge: () => {\n    return !Browser.isIE() && !!window.StyleMedia\n  },\n  // Chrome 1+\n  isChrome: (context = window) => {\n    return !!context.chrome\n  },\n  // At least Safari 3+: \"[object HTMLElementConstructor]\"\n  isSafari: () => {\n    return Object.prototype.toString.call(window.HTMLElement).indexOf('Constructor') > 0 ||\n        navigator.userAgent.toLowerCase().indexOf('safari') !== -1\n  },\n  // IOS Chrome\n  isIOSChrome: () => {\n    return navigator.userAgent.toLowerCase().indexOf('crios') !== -1\n  }\n}\n\nexport default Browser\n", "import Modal from './modal'\nimport Browser from './browser'\n\nexport function addWrapper (htmlData, params) {\n  const bodyStyle = 'font-family:' + params.font + ' !important; font-size: ' + params.font_size + ' !important; width:100%;'\n  return '<div style=\"' + bodyStyle + '\">' + htmlData + '</div>'\n}\n\nexport function capitalizePrint (obj) {\n  return obj.charAt(0).toUpperCase() + obj.slice(1)\n}\n\nexport function collectStyles (element, params) {\n  const win = document.defaultView || window\n\n  // String variable to hold styling for each element\n  let elementStyle = ''\n\n  // Loop over computed styles\n  const styles = win.getComputedStyle(element, '')\n\n  for (let key = 0; key < styles.length; key++) {\n    // Check if style should be processed\n    if (params.targetStyles.indexOf('*') !== -1 || params.targetStyle.indexOf(styles[key]) !== -1 || targetStylesMatch(params.targetStyles, styles[key])) {\n      if (styles.getPropertyValue(styles[key])) elementStyle += styles[key] + ':' + styles.getPropertyValue(styles[key]) + ';'\n    }\n  }\n\n  // Print friendly defaults (deprecated)\n  elementStyle += 'max-width: ' + params.maxWidth + 'px !important; font-size: ' + params.font_size + ' !important;'\n\n  return elementStyle\n}\n\nfunction targetStylesMatch (styles, value) {\n  for (let i = 0; i < styles.length; i++) {\n    if (typeof value === 'object' && value.indexOf(styles[i]) !== -1) return true\n  }\n  return false\n}\n\nexport function addHeader (printElement, params) {\n  // Create the header container div\n  const headerContainer = document.createElement('div')\n\n  // Check if the header is text or raw html\n  if (isRawHTML(params.header)) {\n    headerContainer.innerHTML = params.header\n  } else {\n    // Create header element\n    const headerElement = document.createElement('h1')\n\n    // Create header text node\n    const headerNode = document.createTextNode(params.header)\n\n    // Build and style\n    headerElement.appendChild(headerNode)\n    headerElement.setAttribute('style', params.headerStyle)\n    headerContainer.appendChild(headerElement)\n  }\n\n  printElement.insertBefore(headerContainer, printElement.childNodes[0])\n}\n\nexport function cleanUp (params) {\n  // If we are showing a feedback message to user, remove it\n  if (params.showModal) Modal.close()\n\n  // Check for a finished loading hook function\n  if (params.onLoadingEnd) params.onLoadingEnd()\n\n  // If preloading pdf files, clean blob url\n  if (params.showModal || params.onLoadingStart) window.URL.revokeObjectURL(params.printable)\n\n  // Run onPrintDialogClose callback\n  let event = 'mouseover'\n\n  if (Browser.isChrome() || Browser.isFirefox()) {\n    // Ps.: Firefox will require an extra click in the document to fire the focus event.\n    event = 'focus'\n  }\n\n  const handler = () => {\n    // Make sure the event only happens once.\n    window.removeEventListener(event, handler)\n\n    params.onPrintDialogClose()\n\n    // Remove iframe from the DOM\n    const iframe = document.getElementById(params.frameId)\n\n    if (iframe) {\n      iframe.remove()\n    }\n  }\n\n  window.addEventListener(event, handler)\n}\n\nexport function isRawHTML (raw) {\n  const regexHtml = new RegExp('<([A-Za-z][A-Za-z0-9]*)\\\\b[^>]*>(.*?)</\\\\1>')\n  return regexHtml.test(raw)\n}\n", "import { collectStyles, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Get the DOM printable element\n    const printElement = isHtmlElement(params.printable) ? params.printable : document.getElementById(params.printable)\n\n    // Check if the element exists\n    if (!printElement) {\n      window.console.error('Invalid HTML element id: ' + params.printable)\n      return\n    }\n\n    // Clone the target element including its children (if available)\n    params.printableElement = cloneElement(printElement, params)\n\n    // Add header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Print html element contents\n    Print.send(params, printFrame)\n  }\n}\n\nfunction cloneElement (element, params) {\n  // Clone the main node (if not already inside the recursion process)\n  const clone = element.cloneNode()\n\n  // Loop over and process the children elements / nodes (including text nodes)\n  const childNodesArray = Array.prototype.slice.call(element.childNodes)\n  for (let i = 0; i < childNodesArray.length; i++) {\n    // Check if we are skipping the current element\n    if (params.ignoreElements.indexOf(childNodesArray[i].id) !== -1) {\n      continue\n    }\n\n    // Clone the child element\n    const clonedChild = cloneElement(childNodesArray[i], params)\n\n    // Attach the cloned child to the cloned parent node\n    clone.appendChild(clonedChild)\n  }\n\n  // Get all styling for print element (for nodes of type element only)\n  if (params.scanStyles && element.nodeType === 1) {\n    clone.setAttribute('style', collectStyles(element, params))\n  }\n\n  // Check if the element needs any state processing (copy user input data)\n  switch (element.tagName) {\n    case 'SELECT':\n      // Copy the current selection value to its clone\n      clone.value = element.value\n      break\n    case 'CANVAS':\n      // Copy the canvas content to its clone\n      clone.getContext('2d').drawImage(element, 0, 0)\n      break\n  }\n\n  return clone\n}\n\nfunction isHtmlElement (printable) {\n  // Check if element is instance of HTMLElement or has nodeType === 1 (for elements in iframe)\n  return typeof printable === 'object' && printable && (printable instanceof HTMLElement || printable.nodeType === 1)\n}\n", "import { addHeader } from './functions'\nimport Print from './print'\nimport Browser from './browser'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we are printing one image or multiple images\n    if (params.printable.constructor !== Array) {\n      // Create array with one image\n      params.printable = [params.printable]\n    }\n\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n\n    // Create all image elements and append them to the printable container\n    params.printable.forEach(src => {\n      // Create the image element\n      const img = document.createElement('img')\n      img.setAttribute('style', params.imageStyle)\n\n      // Set image src with the file url\n      img.src = src\n\n      // The following block is for Firefox, which for some reason requires the image's src to be fully qualified in\n      // order to print it\n      if (Browser.isFirefox()) {\n        const fullyQualifiedSrc = img.src\n        img.src = fullyQualifiedSrc\n      }\n\n      // Create the image wrapper\n      const imageWrapper = document.createElement('div')\n\n      // Append image to the wrapper element\n      imageWrapper.appendChild(img)\n\n      // Append wrapper to the printable element\n      params.printableElement.appendChild(imageWrapper)\n    })\n\n    // Check if we are adding a print header\n    if (params.header) addHeader(params.printableElement, params)\n\n    // Print image\n    Print.send(params, printFrame)\n  }\n}\n", "'use strict'\n\nimport Browser from './browser'\nimport Modal from './modal'\nimport Pdf from './pdf'\nimport Html from './html'\nimport RawHtml from './raw-html'\nimport Image from './image'\nimport J<PERSON> from './json'\n\nconst printTypes = ['pdf', 'html', 'image', 'json', 'raw-html']\n\nexport default {\n  init () {\n    const params = {\n      printable: null,\n      fallbackPrintable: null,\n      type: 'pdf',\n      header: null,\n      headerStyle: 'font-weight: 300;',\n      maxWidth: 800,\n      properties: null,\n      gridHeaderStyle: 'font-weight: bold; padding: 5px; border: 1px solid #dddddd;',\n      gridStyle: 'border: 1px solid lightgray; margin-bottom: -1px;',\n      showModal: false,\n      onError: (error) => { throw error },\n      onLoadingStart: null,\n      onLoadingEnd: null,\n      onPrintDialogClose: () => {},\n      onIncompatibleBrowser: () => {},\n      modalMessage: 'Retrieving Document...',\n      frameId: 'printJS',\n      printableElement: null,\n      documentTitle: 'Document',\n      targetStyle: ['clear', 'display', 'width', 'min-width', 'height', 'min-height', 'max-height'],\n      targetStyles: ['border', 'box', 'break', 'text-decoration'],\n      ignoreElements: [],\n      repeatTableHeader: true,\n      css: null,\n      style: null,\n      scanStyles: true,\n      base64: false,\n\n      // Deprecated\n      onPdfOpen: null,\n      font: 'TimesNewRoman',\n      font_size: '12pt',\n      honorMarginPadding: true,\n      honorColor: false,\n      imageStyle: 'max-width: 100%;'\n    }\n\n    // Check if a printable document or object was supplied\n    const args = arguments[0]\n    if (args === undefined) {\n      throw new Error('printJS expects at least 1 attribute.')\n    }\n\n    // Process parameters\n    switch (typeof args) {\n      case 'string':\n        params.printable = encodeURI(args)\n        params.fallbackPrintable = params.printable\n        params.type = arguments[1] || params.type\n        break\n      case 'object':\n        params.printable = args.printable\n        params.fallbackPrintable = typeof args.fallbackPrintable !== 'undefined' ? args.fallbackPrintable : params.printable\n        params.fallbackPrintable = params.base64 ? `data:application/pdf;base64,${params.fallbackPrintable}` : params.fallbackPrintable\n        for (var k in params) {\n          if (k === 'printable' || k === 'fallbackPrintable') continue\n\n          params[k] = typeof args[k] !== 'undefined' ? args[k] : params[k]\n        }\n        break\n      default:\n        throw new Error('Unexpected argument type! Expected \"string\" or \"object\", got ' + typeof args)\n    }\n\n    // Validate printable\n    if (!params.printable) throw new Error('Missing printable information.')\n\n    // Validate type\n    if (!params.type || typeof params.type !== 'string' || printTypes.indexOf(params.type.toLowerCase()) === -1) {\n      throw new Error('Invalid print type. Available types are: pdf, html, image and json.')\n    }\n\n    // Check if we are showing a feedback message to the user (useful for large files)\n    if (params.showModal) Modal.show(params)\n\n    // Check for a print start hook function\n    if (params.onLoadingStart) params.onLoadingStart()\n\n    // To prevent duplication and issues, remove any used printFrame from the DOM\n    const usedFrame = document.getElementById(params.frameId)\n\n    if (usedFrame) usedFrame.parentNode.removeChild(usedFrame)\n\n    // Create a new iframe for the print job\n    const printFrame = document.createElement('iframe')\n\n    if (Browser.isFirefox()) {\n      // Set the iframe to be is visible on the page (guaranteed by fixed position) but hidden using opacity 0, because\n      // this works in Firefox. The height needs to be sufficient for some part of the document other than the PDF\n      // viewer's toolbar to be visible in the page\n      printFrame.setAttribute('style', 'width: 1px; height: 100px; position: fixed; left: 0; top: 0; opacity: 0; border-width: 0; margin: 0; padding: 0')\n    } else {\n      // Hide the iframe in other browsers\n      printFrame.setAttribute('style', 'visibility: hidden; height: 0; width: 0; position: absolute; border: 0')\n    }\n\n    // Set iframe element id\n    printFrame.setAttribute('id', params.frameId)\n\n    // For non pdf printing, pass an html document string to srcdoc (force onload callback)\n    if (params.type !== 'pdf') {\n      printFrame.srcdoc = '<html><head><title>' + params.documentTitle + '</title>'\n\n      // Attach css files\n      if (params.css) {\n        // Add support for single file\n        if (!Array.isArray(params.css)) params.css = [params.css]\n\n        // Create link tags for each css file\n        params.css.forEach(file => {\n          printFrame.srcdoc += '<link rel=\"stylesheet\" href=\"' + file + '\">'\n        })\n      }\n\n      printFrame.srcdoc += '</head><body></body></html>'\n    }\n\n    // Check printable type\n    switch (params.type) {\n      case 'pdf':\n        // Check browser support for pdf and if not supported we will just open the pdf file instead\n        if (Browser.isIE()) {\n          try {\n            console.info('Print.js doesn\\'t support PDF printing in Internet Explorer.')\n            const win = window.open(params.fallbackPrintable, '_blank')\n            win.focus()\n            params.onIncompatibleBrowser()\n          } catch (error) {\n            params.onError(error)\n          } finally {\n            // Make sure there is no loading modal opened\n            if (params.showModal) Modal.close()\n            if (params.onLoadingEnd) params.onLoadingEnd()\n          }\n        } else {\n          Pdf.print(params, printFrame)\n        }\n        break\n      case 'image':\n        Image.print(params, printFrame)\n        break\n      case 'html':\n        Html.print(params, printFrame)\n        break\n      case 'raw-html':\n        RawHtml.print(params, printFrame)\n        break\n      case 'json':\n        Json.print(params, printFrame)\n        break\n    }\n  }\n}\n", "import { capitalizePrint, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we received proper data\n    if (typeof params.printable !== 'object') {\n      throw new Error('Invalid javascript data object (JSON).')\n    }\n\n    // Validate repeatTableHeader\n    if (typeof params.repeatTableHeader !== 'boolean') {\n      throw new Error('Invalid value for repeatTableHeader attribute (JSON).')\n    }\n\n    // Validate properties\n    if (!params.properties || !Array.isArray(params.properties)) {\n      throw new Error('Invalid properties array for your JSON data.')\n    }\n\n    // We will format the property objects to keep the JSON api compatible with older releases\n    params.properties = params.properties.map(property => {\n      return {\n        field: typeof property === 'object' ? property.field : property,\n        displayName: typeof property === 'object' ? property.displayName : property,\n        columnSize: typeof property === 'object' && property.columnSize ? property.columnSize + ';' : 100 / params.properties.length + '%;'\n      }\n    })\n\n    // Create a print container element\n    params.printableElement = document.createElement('div')\n\n    // Check if we are adding a print header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Build the printable html data\n    params.printableElement.innerHTML += jsonToHTML(params)\n\n    // Print the json data\n    Print.send(params, printFrame)\n  }\n}\n\nfunction jsonToHTML (params) {\n  // Get the row and column data\n  const data = params.printable\n  const properties = params.properties\n\n  // Create a html table\n  let htmlData = '<table style=\"border-collapse: collapse; width: 100%;\">'\n\n  // Check if the header should be repeated\n  if (params.repeatTableHeader) {\n    htmlData += '<thead>'\n  }\n\n  // Add the table header row\n  htmlData += '<tr>'\n\n  // Add the table header columns\n  for (let a = 0; a < properties.length; a++) {\n    htmlData += '<th style=\"width:' + properties[a].columnSize + ';' + params.gridHeaderStyle + '\">' + capitalizePrint(properties[a].displayName) + '</th>'\n  }\n\n  // Add the closing tag for the table header row\n  htmlData += '</tr>'\n\n  // If the table header is marked as repeated, add the closing tag\n  if (params.repeatTableHeader) {\n    htmlData += '</thead>'\n  }\n\n  // Create the table body\n  htmlData += '<tbody>'\n\n  // Add the table data rows\n  for (let i = 0; i < data.length; i++) {\n    // Add the row starting tag\n    htmlData += '<tr>'\n\n    // Print selected properties only\n    for (let n = 0; n < properties.length; n++) {\n      let stringData = data[i]\n\n      // Support nested objects\n      const property = properties[n].field.split('.')\n      if (property.length > 1) {\n        for (let p = 0; p < property.length; p++) {\n          stringData = stringData[property[p]]\n        }\n      } else {\n        stringData = stringData[properties[n].field]\n      }\n\n      // Add the row contents and styles\n      htmlData += '<td style=\"width:' + properties[n].columnSize + params.gridStyle + '\">' + stringData + '</td>'\n    }\n\n    // Add the row closing tag\n    htmlData += '</tr>'\n  }\n\n  // Add the table and body closing tags\n  htmlData += '</tbody></table>'\n\n  return htmlData\n}\n", "const Modal = {\n  show (params) {\n    // Build modal\n    const modalStyle = 'font-family:sans-serif; ' +\n        'display:table; ' +\n        'text-align:center; ' +\n        'font-weight:300; ' +\n        'font-size:30px; ' +\n        'left:0; top:0;' +\n        'position:fixed; ' +\n        'z-index: 9990;' +\n        'color: #0460B5; ' +\n        'width: 100%; ' +\n        'height: 100%; ' +\n        'background-color:rgba(255,255,255,.9);' +\n        'transition: opacity .3s ease;'\n\n    // Create wrapper\n    const printModal = document.createElement('div')\n    printModal.setAttribute('style', modalStyle)\n    printModal.setAttribute('id', 'printJS-Modal')\n\n    // Create content div\n    const contentDiv = document.createElement('div')\n    contentDiv.setAttribute('style', 'display:table-cell; vertical-align:middle; padding-bottom:100px;')\n\n    // Add close button (requires print.css)\n    const closeButton = document.createElement('div')\n    closeButton.setAttribute('class', 'printClose')\n    closeButton.setAttribute('id', 'printClose')\n    contentDiv.appendChild(closeButton)\n\n    // Add spinner (requires print.css)\n    const spinner = document.createElement('span')\n    spinner.setAttribute('class', 'printSpinner')\n    contentDiv.appendChild(spinner)\n\n    // Add message\n    const messageNode = document.createTextNode(params.modalMessage)\n    contentDiv.appendChild(messageNode)\n\n    // Add contentDiv to printModal\n    printModal.appendChild(contentDiv)\n\n    // Append print modal element to document body\n    document.getElementsByTagName('body')[0].appendChild(printModal)\n\n    // Add event listener to close button\n    document.getElementById('printClose').addEventListener('click', function () {\n      Modal.close()\n    })\n  },\n  close () {\n    const printModal = document.getElementById('printJS-Modal')\n\n    if (printModal) {\n      printModal.parentNode.removeChild(printModal)\n    }\n  }\n}\n\nexport default Modal\n", "import Print from './print'\nimport { cleanUp } from './functions'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we have base64 data\n    if (params.base64) {\n      const bytesArray = Uint8Array.from(atob(params.printable), c => c.charCodeAt(0))\n      createBlobAndPrint(params, printFrame, bytesArray)\n      return\n    }\n\n    // Format pdf url\n    params.printable = /^(blob|http|\\/\\/)/i.test(params.printable)\n      ? params.printable\n      : window.location.origin + (params.printable.charAt(0) !== '/' ? '/' + params.printable : params.printable)\n\n    // Get the file through a http request (Preload)\n    const req = new window.XMLHttpRequest()\n    req.responseType = 'arraybuffer'\n\n    req.addEventListener('error', () => {\n      cleanUp(params)\n      params.onError(req.statusText, req)\n\n      // Since we don't have a pdf document available, we will stop the print job\n    })\n\n    req.addEventListener('load', () => {\n      // Check for errors\n      if ([200, 201].indexOf(req.status) === -1) {\n        cleanUp(params)\n        params.onError(req.statusText, req)\n\n        // Since we don't have a pdf document available, we will stop the print job\n        return\n      }\n\n      // Print requested document\n      createBlobAndPrint(params, printFrame, req.response)\n    })\n\n    req.open('GET', params.printable, true)\n    req.send()\n  }\n}\n\nfunction createBlobAndPrint (params, printFrame, data) {\n  // Pass response or base64 data to a blob and create a local object url\n  let localPdf = new window.Blob([data], { type: 'application/pdf' })\n  localPdf = window.URL.createObjectURL(localPdf)\n\n  // Set iframe src with pdf document url\n  printFrame.setAttribute('src', localPdf)\n\n  Print.send(params, printFrame)\n}\n", "import Browser from './browser'\nimport { cleanUp } from './functions'\n\nconst Print = {\n  send: (params, printFrame) => {\n    // Append iframe element to document body\n    document.getElementsByTagName('body')[0].appendChild(printFrame)\n\n    // Get iframe element\n    const iframeElement = document.getElementById(params.frameId)\n\n    // Wait for iframe to load all content\n    iframeElement.onload = () => {\n      if (params.type === 'pdf') {\n        // Add a delay for Firefox. In my tests, 1000ms was sufficient but 100ms was not\n        if (Browser.isFirefox()) {\n          setTimeout(() => performPrint(iframeElement, params), 1000)\n        } else {\n          performPrint(iframeElement, params)\n        }\n        return\n      }\n\n      // Get iframe element document\n      let printDocument = (iframeElement.contentWindow || iframeElement.contentDocument)\n      if (printDocument.document) printDocument = printDocument.document\n\n      // Append printable element to the iframe body\n      printDocument.body.appendChild(params.printableElement)\n\n      // Add custom style\n      if (params.type !== 'pdf' && params.style) {\n        // Create style element\n        const style = document.createElement('style')\n        style.innerHTML = params.style\n\n        // Append style element to iframe's head\n        printDocument.head.appendChild(style)\n      }\n\n      // If printing images, wait for them to load inside the iframe\n      const images = printDocument.getElementsByTagName('img')\n\n      if (images.length > 0) {\n        loadIframeImages(Array.from(images)).then(() => performPrint(iframeElement, params))\n      } else {\n        performPrint(iframeElement, params)\n      }\n    }\n  }\n}\n\nfunction performPrint (iframeElement, params) {\n  try {\n    iframeElement.focus()\n\n    // If Edge or IE, try catch with execCommand\n    if (Browser.isEdge() || Browser.isIE()) {\n      try {\n        iframeElement.contentWindow.document.execCommand('print', false, null)\n      } catch (e) {\n        iframeElement.contentWindow.print()\n      }\n    } else {\n      // Other browsers\n      iframeElement.contentWindow.print()\n    }\n  } catch (error) {\n    params.onError(error)\n  } finally {\n    if (Browser.isFirefox()) {\n      // Move the iframe element off-screen and make it invisible\n      iframeElement.style.visibility = 'hidden'\n      iframeElement.style.left = '-1px'\n    }\n\n    cleanUp(params)\n  }\n}\n\nfunction loadIframeImages (images) {\n  const promises = images.map(image => {\n    if (image.src && image.src !== window.location.href) {\n      return loadIframeImage(image)\n    }\n  })\n\n  return Promise.all(promises)\n}\n\nfunction loadIframeImage (image) {\n  return new Promise(resolve => {\n    const pollImage = () => {\n      !image || typeof image.naturalWidth === 'undefined' || image.naturalWidth === 0 || !image.complete\n        ? setTimeout(pollImage, 500)\n        : resolve()\n    }\n    pollImage()\n  })\n}\n\nexport default Print\n", "import Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n    params.printableElement.setAttribute('style', 'width:100%')\n\n    // Set our raw html as the printable element inner html content\n    params.printableElement.innerHTML = params.printable\n\n    // Print html contents\n    Print.send(params, printFrame)\n  }\n}\n", "// extracted by mini-css-extract-plugin"], "sourceRoot": ""}