<template>
  <div class="printing-guide">
    <div class="guide-header">
      <h2>📖 使用指南</h2>
      <button @click="toggleGuide" class="toggle-btn">
        {{ showGuide ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="showGuide" class="guide-content">
      <div class="guide-section">
        <h3>🖨️ 打印方式选择</h3>
        <div class="method-comparison">
          <div class="method-card recommended">
            <h4>浏览器打印 (推荐)</h4>
            <div class="pros-cons">
              <div class="pros">
                <strong>优点：</strong>
                <ul>
                  <li>兼容性最好，支持所有打印机</li>
                  <li>使用系统默认打印机</li>
                  <li>支持所有现代浏览器</li>
                  <li>可以设置打印参数</li>
                </ul>
              </div>
              <div class="cons">
                <strong>缺点：</strong>
                <ul>
                  <li>可能会显示系统打印对话框</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="method-card">
            <h4>串口直连</h4>
            <div class="pros-cons">
              <div class="pros">
                <strong>优点：</strong>
                <ul>
                  <li>真正的无预览直接打印</li>
                  <li>支持ESC/POS命令</li>
                  <li>适合热敏打印机</li>
                </ul>
              </div>
              <div class="cons">
                <strong>缺点：</strong>
                <ul>
                  <li>只支持Chrome/Edge浏览器</li>
                  <li>大多数USB打印机不支持</li>
                  <li>需要用户授权设备访问</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="method-card">
            <h4>PDF导出</h4>
            <div class="pros-cons">
              <div class="pros">
                <strong>优点：</strong>
                <ul>
                  <li>便于保存和分享</li>
                  <li>支持所有浏览器</li>
                  <li>格式固定，不会变形</li>
                </ul>
              </div>
              <div class="cons">
                <strong>缺点：</strong>
                <ul>
                  <li>需要额外步骤打印PDF</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="guide-section">
        <h3>🔧 故障排除</h3>
        <div class="troubleshooting">
          <div class="issue">
            <h4>❌ 串口连接失败："找不到兼容设备"</h4>
            <div class="solution">
              <p><strong>原因：</strong>大多数USB打印机使用USB打印协议，不支持串口通信。</p>
              <p><strong>解决方案：</strong></p>
              <ol>
                <li>切换到"浏览器打印"方式（推荐）</li>
                <li>或使用"PDF导出"方式</li>
                <li>如果确实需要串口打印，请使用支持串口的热敏打印机</li>
              </ol>
            </div>
          </div>
          
          <div class="issue">
            <h4>❌ 浏览器打印没有反应</h4>
            <div class="solution">
              <p><strong>可能原因：</strong></p>
              <ol>
                <li>浏览器阻止了弹窗 - 请允许弹窗</li>
                <li>没有安装打印机驱动 - 请安装打印机驱动</li>
                <li>打印机离线 - 请检查打印机状态</li>
              </ol>
            </div>
          </div>
          
          <div class="issue">
            <h4>❌ 打印内容格式不正确</h4>
            <div class="solution">
              <p><strong>解决方案：</strong></p>
              <ol>
                <li>调整字体大小设置</li>
                <li>选择合适的纸张规格</li>
                <li>使用预设模板作为参考</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
      
      <div class="guide-section">
        <h3>💡 使用技巧</h3>
        <div class="tips">
          <div class="tip">
            <h4>🎯 最佳实践</h4>
            <ul>
              <li>首次使用建议选择"浏览器打印"方式</li>
              <li>使用预设模板可以快速开始</li>
              <li>打印前先使用"打印测试页"检查连接</li>
              <li>保存常用的打印内容作为模板</li>
            </ul>
          </div>
          
          <div class="tip">
            <h4>⚙️ 设置建议</h4>
            <ul>
              <li>收据打印：选择58mm或80mm纸张，字体12-14px</li>
              <li>标签打印：选择A4纸张，字体14-16px</li>
              <li>文档打印：选择A4纸张，字体12-14px</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="guide-section">
        <h3>🌐 浏览器兼容性</h3>
        <div class="browser-support">
          <div class="browser-item supported">
            <span class="browser-name">Chrome 89+</span>
            <span class="support-level">✅ 完全支持</span>
          </div>
          <div class="browser-item supported">
            <span class="browser-name">Edge 89+</span>
            <span class="support-level">✅ 完全支持</span>
          </div>
          <div class="browser-item partial">
            <span class="browser-name">Firefox</span>
            <span class="support-level">⚠️ 仅浏览器打印</span>
          </div>
          <div class="browser-item partial">
            <span class="browser-name">Safari</span>
            <span class="support-level">⚠️ 仅浏览器打印</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showGuide = ref(false)

const toggleGuide = () => {
  showGuide.value = !showGuide.value
}
</script>

<style scoped>
.printing-guide {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
}

.guide-header h2 {
  margin: 0;
  color: #495057;
  font-size: 1.2rem;
}

.toggle-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.toggle-btn:hover {
  background: #0056b3;
}

.guide-content {
  padding: 20px;
}

.guide-section {
  margin-bottom: 30px;
}

.guide-section h3 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

/* 方式对比样式 */
.method-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.method-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}

.method-card.recommended {
  border-color: #28a745;
  background: #f8fff9;
}

.method-card h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.method-card.recommended h4 {
  color: #28a745;
}

.pros-cons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pros, .cons {
  font-size: 13px;
}

.pros strong {
  color: #28a745;
}

.cons strong {
  color: #dc3545;
}

.pros ul, .cons ul {
  margin: 5px 0 0 0;
  padding-left: 20px;
}

.pros li, .cons li {
  margin-bottom: 3px;
}

/* 故障排除样式 */
.troubleshooting {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.issue {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}

.issue h4 {
  margin: 0 0 10px 0;
  color: #dc3545;
  font-size: 14px;
}

.solution {
  font-size: 13px;
  color: #495057;
}

.solution p {
  margin: 5px 0;
}

.solution ol {
  margin: 5px 0 0 0;
  padding-left: 20px;
}

/* 技巧样式 */
.tips {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.tip {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}

.tip h4 {
  margin: 0 0 10px 0;
  color: #007bff;
  font-size: 14px;
}

.tip ul {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.tip li {
  margin-bottom: 5px;
}

/* 浏览器支持样式 */
.browser-support {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.browser-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
}

.browser-item.supported {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.browser-item.partial {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
}

.browser-name {
  font-weight: bold;
}

.support-level {
  font-size: 12px;
}

@media (max-width: 768px) {
  .method-comparison {
    grid-template-columns: 1fr;
  }
  
  .tips {
    grid-template-columns: 1fr;
  }
  
  .browser-support {
    grid-template-columns: 1fr;
  }
  
  .guide-header {
    padding: 10px 15px;
  }
  
  .guide-content {
    padding: 15px;
  }
}
</style>
