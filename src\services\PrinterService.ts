/**
 * USB打印机服务类
 * 提供连接、打印、格式化等功能
 */

export interface PrinterConfig {
  baudRate: number
  paperWidth: number // mm
  fontSize: number
  autoCut: boolean
  encoding: string
}

export interface PrintJob {
  content: string
  type: 'text' | 'receipt' | 'label' | 'barcode'
  config?: Partial<PrinterConfig>
}

export class PrinterService {
  private port: SerialPort | null = null
  private isConnected = false
  private config: PrinterConfig = {
    baudRate: 9600,
    paperWidth: 80, // 80mm热敏纸
    fontSize: 14,
    autoCut: true,
    encoding: 'utf-8'
  }

  /**
   * 检查浏览器是否支持Web Serial API
   */
  static isSupported(): boolean {
    return 'serial' in navigator
  }

  /**
   * 连接USB打印机
   */
  async connect(): Promise<boolean> {
    try {
      if (!PrinterService.isSupported()) {
        throw new Error('浏览器不支持Web Serial API，请使用Chrome 89+或Edge 89+')
      }

      // 请求用户选择串口设备
      this.port = await (navigator as any).serial.requestPort({
        filters: [
          // 常见的打印机厂商ID
          { usbVendorId: 0x04b8 }, // Epson
          { usbVendorId: 0x04f9 }, // Brother
          { usbVendorId: 0x03f0 }, // HP
          { usbVendorId: 0x0483 }, // STMicroelectronics (一些热敏打印机)
        ]
      })

      // 打开串口连接
      await this.port.open({ 
        baudRate: this.config.baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        flowControl: 'none'
      })

      this.isConnected = true
      console.log('打印机连接成功')
      return true

    } catch (error) {
      console.error('连接打印机失败:', error)
      this.isConnected = false
      throw error
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.port && this.isConnected) {
      try {
        await this.port.close()
        this.port = null
        this.isConnected = false
        console.log('打印机连接已断开')
      } catch (error) {
        console.error('断开连接失败:', error)
      }
    }
  }

  /**
   * 检查连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected && this.port !== null
  }

  /**
   * 更新打印机配置
   */
  updateConfig(newConfig: Partial<PrinterConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 执行打印任务
   */
  async print(job: PrintJob): Promise<void> {
    if (!this.getConnectionStatus()) {
      throw new Error('打印机未连接')
    }

    const writer = this.port!.writable?.getWriter()
    if (!writer) {
      throw new Error('无法获取串口写入器')
    }

    try {
      // 合并配置
      const printConfig = { ...this.config, ...job.config }
      
      // 生成打印命令
      const commands = this.generatePrintCommands(job, printConfig)
      
      // 发送到打印机
      const encoder = new TextEncoder()
      await writer.write(encoder.encode(commands))
      
      console.log('打印任务已发送')
      
    } catch (error) {
      console.error('打印失败:', error)
      throw error
    } finally {
      writer.releaseLock()
    }
  }

  /**
   * 生成ESC/POS打印命令
   */
  private generatePrintCommands(job: PrintJob, config: PrinterConfig): string {
    let commands = ''
    
    // 初始化打印机
    commands += '\x1B\x40' // ESC @ (初始化)
    
    // 设置字符编码 (UTF-8)
    commands += '\x1B\x74\x10' // ESC t 16 (UTF-8)
    
    // 设置字体大小
    commands += this.getFontSizeCommand(config.fontSize)
    
    // 根据打印类型处理内容
    switch (job.type) {
      case 'receipt':
        commands += this.formatReceipt(job.content, config)
        break
      case 'label':
        commands += this.formatLabel(job.content, config)
        break
      case 'barcode':
        commands += this.formatBarcode(job.content, config)
        break
      default:
        commands += this.formatText(job.content, config)
    }
    
    // 换行
    commands += '\n\n'
    
    // 自动切纸
    if (config.autoCut) {
      commands += '\x1D\x56\x00' // GS V 0 (全切)
    } else {
      commands += '\x1D\x56\x01' // GS V 1 (半切)
    }
    
    return commands
  }

  /**
   * 获取字体大小命令
   */
  private getFontSizeCommand(fontSize: number): string {
    if (fontSize <= 12) return '\x1B\x21\x00'      // 正常
    else if (fontSize <= 14) return '\x1B\x21\x10' // 高度2倍
    else if (fontSize <= 16) return '\x1B\x21\x20' // 宽度2倍
    else if (fontSize <= 18) return '\x1B\x21\x30' // 高度和宽度2倍
    else return '\x1B\x21\x40'                      // 更大字体
  }

  /**
   * 格式化普通文本
   */
  private formatText(content: string, config: PrinterConfig): string {
    // 根据纸张宽度自动换行
    const maxChars = Math.floor(config.paperWidth / (config.fontSize * 0.6))
    return this.wrapText(content, maxChars)
  }

  /**
   * 格式化收据
   */
  private formatReceipt(content: string, config: PrinterConfig): string {
    let formatted = ''
    
    // 居中标题
    formatted += '\x1B\x61\x01' // ESC a 1 (居中对齐)
    formatted += '收据\n'
    formatted += '\x1B\x61\x00' // ESC a 0 (左对齐)
    
    // 分隔线
    formatted += '='.repeat(32) + '\n'
    
    // 内容
    formatted += content + '\n'
    
    // 分隔线
    formatted += '='.repeat(32) + '\n'
    
    return formatted
  }

  /**
   * 格式化标签
   */
  private formatLabel(content: string, config: PrinterConfig): string {
    let formatted = ''
    
    // 标签边框
    formatted += '┌' + '─'.repeat(30) + '┐\n'
    
    // 内容
    const lines = content.split('\n')
    for (const line of lines) {
      formatted += '│ ' + line.padEnd(28) + ' │\n'
    }
    
    // 底部边框
    formatted += '└' + '─'.repeat(30) + '┘\n'
    
    return formatted
  }

  /**
   * 格式化条形码
   */
  private formatBarcode(content: string, config: PrinterConfig): string {
    let formatted = ''
    
    // 设置条形码类型 (CODE128)
    formatted += '\x1D\x6B\x49' // GS k I
    formatted += String.fromCharCode(content.length) // 长度
    formatted += content // 条形码数据
    
    // 条形码下方显示文字
    formatted += '\n' + content + '\n'
    
    return formatted
  }

  /**
   * 文本自动换行
   */
  private wrapText(text: string, maxChars: number): string {
    const lines = text.split('\n')
    const wrappedLines: string[] = []
    
    for (const line of lines) {
      if (line.length <= maxChars) {
        wrappedLines.push(line)
      } else {
        // 按字符数分割
        for (let i = 0; i < line.length; i += maxChars) {
          wrappedLines.push(line.substr(i, maxChars))
        }
      }
    }
    
    return wrappedLines.join('\n')
  }

  /**
   * 打印测试页
   */
  async printTestPage(): Promise<void> {
    const testContent = `打印机测试页面

连接状态: 正常
纸张宽度: ${this.config.paperWidth}mm
字体大小: ${this.config.fontSize}px
波特率: ${this.config.baudRate}

字符测试:
ABCDEFGHIJKLMNOPQRSTUVWXYZ
abcdefghijklmnopqrstuvwxyz
0123456789
!@#$%^&*()_+-=[]{}|;:,.<>?

中文测试:
你好世界！这是打印测试。
测试时间: ${new Date().toLocaleString()}

测试完成！`

    await this.print({
      content: testContent,
      type: 'text'
    })
  }
}

// 创建全局打印机服务实例
export const printerService = new PrinterService()
