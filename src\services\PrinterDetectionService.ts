/**
 * 打印机检测和管理服务
 * 支持多种打印方式：系统打印机、网络打印机、直接打印等
 */

export interface PrinterInfo {
  id: string
  name: string
  type: 'system' | 'network' | 'usb' | 'virtual'
  status: 'online' | 'offline' | 'unknown'
  isDefault?: boolean
}

export interface PrintOptions {
  printerName?: string
  copies?: number
  paperSize?: string
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  silent?: boolean // 静默打印，不显示对话框
}

export class PrinterDetectionService {
  private availablePrinters: PrinterInfo[] = []
  private defaultPrinter: PrinterInfo | null = null

  /**
   * 检测可用的打印机
   */
  async detectPrinters(): Promise<PrinterInfo[]> {
    const printers: PrinterInfo[] = []

    // 1. 检测系统默认打印机
    try {
      const systemPrinters = await this.getSystemPrinters()
      printers.push(...systemPrinters)
    } catch (error) {
      console.warn('无法检测系统打印机:', error)
    }

    // 2. 检测Web Serial API支持的设备
    try {
      if ('serial' in navigator) {
        const serialPrinters = await this.getSerialPrinters()
        printers.push(...serialPrinters)
      }
    } catch (error) {
      console.warn('无法检测串口打印机:', error)
    }

    // 3. 添加虚拟打印机选项
    printers.push({
      id: 'browser-print',
      name: '浏览器打印 (推荐)',
      type: 'virtual',
      status: 'online',
      isDefault: printers.length === 0
    })

    printers.push({
      id: 'pdf-export',
      name: 'PDF导出',
      type: 'virtual',
      status: 'online'
    })

    this.availablePrinters = printers
    this.defaultPrinter = printers.find(p => p.isDefault) || printers[0] || null

    return printers
  }

  /**
   * 获取系统打印机列表
   */
  private async getSystemPrinters(): Promise<PrinterInfo[]> {
    // 注意：Web API无法直接访问系统打印机列表
    // 这里提供一个模拟实现，实际项目中可能需要后端支持
    
    const printers: PrinterInfo[] = []

    // 尝试使用实验性的打印机API（如果可用）
    if ('getInstalledRelatedApps' in navigator) {
      try {
        // 这是一个实验性API，可能不可用
        const apps = await (navigator as any).getInstalledRelatedApps()
        console.log('检测到的应用:', apps)
      } catch (error) {
        console.log('实验性API不可用')
      }
    }

    // 添加常见的系统打印机名称作为示例
    const commonPrinters = [
      'Microsoft Print to PDF',
      'Microsoft XPS Document Writer',
      'Fax'
    ]

    commonPrinters.forEach((name, index) => {
      printers.push({
        id: `system-${index}`,
        name: name,
        type: 'system',
        status: 'unknown',
        isDefault: index === 0
      })
    })

    return printers
  }

  /**
   * 获取串口打印机列表
   */
  private async getSerialPrinters(): Promise<PrinterInfo[]> {
    const printers: PrinterInfo[] = []

    try {
      // 获取已授权的串口设备
      const ports = await (navigator as any).serial.getPorts()
      
      ports.forEach((port: any, index: number) => {
        const info = port.getInfo()
        printers.push({
          id: `serial-${index}`,
          name: `串口打印机 ${index + 1} (VID: ${info.usbVendorId || 'Unknown'})`,
          type: 'usb',
          status: 'online'
        })
      })
    } catch (error) {
      console.warn('获取串口设备失败:', error)
    }

    return printers
  }

  /**
   * 请求连接新的串口设备
   */
  async requestSerialPrinter(): Promise<PrinterInfo | null> {
    try {
      if (!('serial' in navigator)) {
        throw new Error('浏览器不支持Web Serial API')
      }

      const port = await (navigator as any).serial.requestPort({
        filters: [
          // 常见打印机厂商的USB VID
          { usbVendorId: 0x04b8 }, // Epson
          { usbVendorId: 0x04f9 }, // Brother
          { usbVendorId: 0x03f0 }, // HP
          { usbVendorId: 0x0483 }, // STMicroelectronics
          { usbVendorId: 0x1a86 }, // QinHeng Electronics
          { usbVendorId: 0x0403 }, // FTDI
        ]
      })

      const info = port.getInfo()
      const printer: PrinterInfo = {
        id: `serial-new-${Date.now()}`,
        name: `新连接打印机 (VID: ${info.usbVendorId})`,
        type: 'usb',
        status: 'online'
      }

      this.availablePrinters.push(printer)
      return printer

    } catch (error) {
      if (error instanceof Error && error.message.includes('No port selected')) {
        throw new Error('用户取消了设备选择')
      }
      throw error
    }
  }

  /**
   * 使用浏览器原生打印API进行打印
   */
  async printWithBrowser(content: string, options: PrintOptions = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 创建打印窗口
        const printWindow = window.open('', '_blank', 'width=800,height=600')
        if (!printWindow) {
          throw new Error('无法创建打印窗口，请检查浏览器弹窗设置')
        }

        // 构建打印页面
        const printContent = this.buildPrintPage(content, options)
        printWindow.document.write(printContent)
        printWindow.document.close()

        // 等待内容加载完成
        printWindow.onload = () => {
          setTimeout(() => {
            try {
              printWindow.print()
              
              // 监听打印完成事件
              printWindow.onafterprint = () => {
                printWindow.close()
                resolve()
              }
              
              // 如果用户取消打印
              setTimeout(() => {
                if (!printWindow.closed) {
                  printWindow.close()
                  resolve()
                }
              }, 1000)
              
            } catch (error) {
              printWindow.close()
              reject(error)
            }
          }, 500)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 构建打印页面HTML
   */
  private buildPrintPage(content: string, options: PrintOptions): string {
    const { paperSize = 'A4', orientation = 'portrait', margins } = options
    
    const marginStyle = margins 
      ? `margin: ${margins.top}mm ${margins.right}mm ${margins.bottom}mm ${margins.left}mm;`
      : 'margin: 20mm;'

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>打印内容</title>
        <style>
          @page {
            size: ${paperSize} ${orientation};
            ${marginStyle}
          }
          
          body {
            font-family: 'Courier New', monospace;
            font-size: 12pt;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #000;
            background: #fff;
          }
          
          .print-content {
            white-space: pre-wrap;
            word-wrap: break-word;
          }
          
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
          }
          
          @media screen {
            body {
              padding: 20px;
              background: #f5f5f5;
            }
            
            .print-content {
              background: white;
              padding: 20px;
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
              max-width: 800px;
              margin: 0 auto;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-content">${this.escapeHtml(content)}</div>
      </body>
      </html>
    `
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 导出为PDF
   */
  async exportToPDF(content: string, filename: string = 'print-content.pdf'): Promise<void> {
    try {
      // 创建临时打印页面
      const printContent = this.buildPrintPage(content, { paperSize: 'A4' })
      
      // 创建隐藏的iframe
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      document.body.appendChild(iframe)
      
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) {
        throw new Error('无法创建打印文档')
      }
      
      iframeDoc.write(printContent)
      iframeDoc.close()
      
      // 等待内容加载
      setTimeout(() => {
        try {
          // 使用浏览器的打印到PDF功能
          iframe.contentWindow?.print()
        } catch (error) {
          console.error('PDF导出失败:', error)
        } finally {
          document.body.removeChild(iframe)
        }
      }, 1000)
      
    } catch (error) {
      throw new Error(`PDF导出失败: ${error}`)
    }
  }

  /**
   * 获取可用打印机列表
   */
  getAvailablePrinters(): PrinterInfo[] {
    return this.availablePrinters
  }

  /**
   * 获取默认打印机
   */
  getDefaultPrinter(): PrinterInfo | null {
    return this.defaultPrinter
  }

  /**
   * 设置默认打印机
   */
  setDefaultPrinter(printerId: string): void {
    const printer = this.availablePrinters.find(p => p.id === printerId)
    if (printer) {
      // 清除之前的默认设置
      this.availablePrinters.forEach(p => p.isDefault = false)
      // 设置新的默认打印机
      printer.isDefault = true
      this.defaultPrinter = printer
    }
  }
}

// 创建全局实例
export const printerDetectionService = new PrinterDetectionService()
