# Vue3 多方式打印 Demo

这是一个使用Vue3开发的多方式打印演示应用，完美解决USB打印机兼容性问题，支持浏览器打印、串口直连、PDF导出等多种打印方式。

## 🚀 功能特性

### 多种打印方式
- ✅ **浏览器打印 (推荐)** - 兼容所有打印机，使用系统默认打印机
- ✅ **串口直连** - 支持热敏打印机，真正无预览打印
- ✅ **PDF导出** - 便于保存和分享，支持所有浏览器

### 强大功能
- ✅ **无预览打印** - 根据选择的方式实现直接打印
- ✅ **智能检测** - 自动检测可用打印机和最佳打印方式
- ✅ **多种纸张** - 支持A4、58mm、80mm等多种纸张规格
- ✅ **灵活配置** - 可调节字体大小、纸张方向等设置
- ✅ **预设模板** - 内置收据、标签、测试页等模板
- ✅ **实时状态** - 显示连接状态和打印进度
- ✅ **完善指导** - 内置使用指南和故障排除

## 🌐 浏览器支持

| 浏览器 | 浏览器打印 | 串口直连 | PDF导出 | 推荐度 |
|--------|------------|----------|---------|--------|
| Chrome 89+ | ✅ | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| Edge 89+ | ✅ | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| Firefox | ✅ | ❌ | ✅ | ⭐⭐⭐ |
| Safari | ✅ | ❌ | ✅ | ⭐⭐⭐ |
| Opera 75+ | ✅ | ✅ | ✅ | ⭐⭐⭐⭐ |

> **推荐使用Chrome或Edge浏览器以获得完整功能支持**

## 🖨️ 打印机兼容性

### 浏览器打印方式
- ✅ **所有类型打印机** - 喷墨、激光、热敏、针式等
- ✅ **所有品牌** - HP、Canon、Epson、Brother等
- ✅ **网络打印机** - 支持WiFi和有线网络打印机
- ✅ **虚拟打印机** - PDF打印机、XPS等

### 串口直连方式
- ✅ **热敏打印机** - 支持ESC/POS命令的58mm/80mm热敏打印机
- ✅ **常见品牌** - Epson、Brother、佳博、汉印等
- ❌ **普通USB打印机** - 大多数喷墨/激光打印机不支持串口通信

## 项目设置

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

## 📖 使用说明

### 快速开始

1. **选择打印方式**
   - 🌟 **推荐：浏览器打印** - 兼容性最好，适合大多数用户
   - ⚡ **串口直连** - 适合热敏打印机，真正无预览
   - 📄 **PDF导出** - 便于保存和后续打印

2. **浏览器打印方式**
   - 点击"检测可用打印机"
   - 选择要使用的打印机
   - 输入打印内容
   - 点击"浏览器打印（无预览）"

3. **串口直连方式**
   - 确保热敏打印机已连接
   - 点击"连接串口打印机"
   - 在弹出框中选择设备
   - 设置纸张规格和字体大小
   - 点击"串口直接打印"

4. **PDF导出方式**
   - 输入要打印的内容
   - 点击"导出为PDF"
   - 保存PDF文件后可用任意打印机打印

### 故障排除

#### ❌ 串口连接失败："找不到兼容设备"
**原因：** 大多数USB打印机使用USB打印协议，不支持串口通信
**解决：** 切换到"浏览器打印"方式（推荐）

#### ❌ 浏览器打印没有反应
**解决：** 检查浏览器弹窗设置，确保允许弹窗

#### ❌ 打印内容格式不正确
**解决：** 调整字体大小和纸张规格设置

## 🏗️ 技术架构

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **打印技术**:
  - Web Serial API (串口直连)
  - Browser Print API (浏览器打印)
  - ESC/POS命令 (热敏打印机)
- **样式**: CSS3 + Flexbox + Grid
- **状态管理**: Vue 3 Composition API

## 📁 核心文件

- `src/components/USBPrinter.vue` - 主要的打印组件
- `src/components/PrintingGuide.vue` - 使用指南组件
- `src/services/PrinterService.ts` - 串口打印机服务类
- `src/services/PrinterDetectionService.ts` - 打印机检测和管理服务
- `src/App.vue` - 应用主页面

## 🎯 最佳实践

1. **首次使用建议选择"浏览器打印"方式**
2. **使用预设模板可以快速开始**
3. **打印前先测试连接状态**
4. **保存常用的打印内容作为模板**

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
