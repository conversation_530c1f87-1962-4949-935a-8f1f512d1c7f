# Vue3 USB打印机 Demo

这是一个使用Vue3开发的USB打印机连接和直接打印的演示应用，支持不弹预览的直接打印功能。

## 功能特性

- ✅ 连接USB打印机（使用Web Serial API）
- ✅ 直接打印文本内容（无预览）
- ✅ 支持多种纸张规格（58mm/80mm热敏纸、A4等）
- ✅ 可调节字体大小
- ✅ 自动切纸功能
- ✅ 预设打印模板（收据、标签、测试页）
- ✅ ESC/POS命令支持
- ✅ 打印机状态监控

## 浏览器支持

此应用使用Web Serial API，需要以下浏览器支持：

- Chrome 89+ ✅
- Edge 89+ ✅
- Opera 75+ ✅
- Firefox ❌ (不支持Web Serial API)
- Safari ❌ (不支持Web Serial API)

## 硬件要求

- USB接口的热敏打印机或针式打印机
- 支持ESC/POS命令的打印机
- 常见品牌：Epson、Brother、HP等

## 项目设置

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

## 使用说明

1. **连接打印机**
   - 确保USB打印机已连接到电脑
   - 点击"连接USB打印机"按钮
   - 在弹出的设备选择框中选择你的打印机

2. **设置打印参数**
   - 选择纸张大小（58mm/80mm热敏纸或A4）
   - 调整字体大小
   - 选择是否自动切纸

3. **输入打印内容**
   - 在文本框中输入要打印的内容
   - 或使用预设模板（收据、标签、测试页）

4. **执行打印**
   - 点击"直接打印（无预览）"进行USB直接打印
   - 或点击"打印HTML内容"使用浏览器打印
   - 或点击"打印测试页"测试打印机功能

## 技术架构

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **打印技术**: Web Serial API + ESC/POS命令
- **样式**: CSS3 + Flexbox
- **状态管理**: Vue 3 Composition API

## 核心文件

- `src/components/USBPrinter.vue` - 主要的打印组件
- `src/services/PrinterService.ts` - 打印机服务类
- `src/App.vue` - 应用主页面
