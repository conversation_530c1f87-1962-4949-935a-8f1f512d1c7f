# Vue3 USB热敏打印机 Demo

这是一个专门针对USB热敏打印机的Vue3应用，使用WebHID API实现真正的无弹窗直接打印，完美适用于收银、标签、小票等热敏打印场景。

## 🖨️ 核心特性

### 专业热敏打印
- ✅ **USB直连** - 使用WebHID API直接连接USB热敏打印机
- ✅ **无弹窗打印** - 真正的静默打印，无任何系统对话框
- ✅ **ESC/POS支持** - 完整的热敏打印机命令支持
- ✅ **多品牌兼容** - 支持Epson、佳博、汉印、芯烨等主流品牌

### 丰富功能
- ✅ **智能检测** - 自动扫描和识别USB热敏打印机
- ✅ **多种规格** - 支持58mm、80mm热敏纸
- ✅ **预设模板** - 收银小票、订单、标签、快递单等6种模板
- ✅ **实时编辑** - 可视化编辑打印内容
- ✅ **即时打印** - 选择模板后一键直接打印
- ✅ **状态监控** - 实时显示打印机连接和打印状态

## 🌐 浏览器支持

| 浏览器 | WebHID支持 | 推荐度 | 说明 |
|--------|------------|--------|------|
| Chrome 89+ | ✅ | ⭐⭐⭐⭐⭐ | 完整支持，推荐使用 |
| Edge 89+ | ✅ | ⭐⭐⭐⭐⭐ | 完整支持，推荐使用 |
| Opera 76+ | ✅ | ⭐⭐⭐⭐ | 支持良好 |
| Firefox | ❌ | ❌ | 不支持WebHID API |
| Safari | ❌ | ❌ | 不支持WebHID API |

> **必须使用支持WebHID API的浏览器才能连接USB热敏打印机**

## 🖨️ 支持的热敏打印机

### 主流品牌
- ✅ **Epson (爱普生)** - TM-T88V, TM-T88VI, TM-T20, TM-T82系列
- ✅ **佳博 (Gprinter)** - GP-58130IVC, GP-80250I系列
- ✅ **汉印 (HPRT)** - N31, N41系列
- ✅ **芯烨 (XINYE)** - XP-58IIH, XP-80C系列
- ✅ **其他品牌** - 使用标准USB接口的热敏打印机

### 技术规格
- ✅ **接口类型** - USB接口（非串口转USB）
- ✅ **纸张规格** - 58mm、80mm热敏纸
- ✅ **命令支持** - ESC/POS标准命令
- ✅ **功能支持** - 文本打印、自动切纸

## 项目设置

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

## 📖 使用说明

### 快速开始

1. **连接USB热敏打印机**
   - 确保热敏打印机已连接到电脑USB口
   - 点击"扫描USB热敏打印机"按钮
   - 或点击"添加新打印机"手动选择设备
   - 在检测到的打印机列表中点击"连接"

2. **选择打印模板**
   - 从6种预设模板中选择：收银小票、订单、标签、快递单、排队号、测试页
   - 或直接在文本框中输入自定义内容
   - 根据需要调整纸张宽度（58mm/80mm）

3. **直接打印**
   - 点击"🖨️ 直接打印到热敏纸"按钮
   - 打印机将立即输出到热敏纸，无任何弹窗
   - 可使用"打印测试页"验证连接状态

### 故障排除

#### ❌ 找不到USB热敏打印机
**可能原因：**
- 打印机未正确连接到电脑
- 打印机驱动未安装
- 使用了不支持的浏览器

**解决方案：**
1. 检查USB连接线是否插好
2. 确认打印机电源已开启
3. 安装打印机官方驱动程序
4. 使用Chrome 89+或Edge 89+浏览器

#### ❌ 连接成功但无法打印
**解决方案：**
1. 检查热敏纸是否正确安装
2. 确认打印机支持ESC/POS命令
3. 尝试打印测试页验证功能
4. 重新插拔USB线缆后重试

#### ❌ 打印内容格式异常
**解决方案：**
1. 调整纸张宽度设置（58mm/80mm）
2. 使用预设模板作为参考
3. 检查打印内容是否包含特殊字符

## 🏗️ 技术架构

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **核心技术**: WebHID API + ESC/POS命令协议
- **样式**: CSS3 + Flexbox + Grid
- **状态管理**: Vue 3 Composition API

## 📁 核心文件

- `src/components/USBThermalPrinter.vue` - USB热敏打印机主组件
- `src/services/USBThermalPrinterService.ts` - USB热敏打印机服务类
- `src/App.vue` - 应用主页面

## 🎯 使用建议

1. **确保使用支持WebHID的浏览器（Chrome 89+/Edge 89+）**
2. **首次使用建议先打印测试页验证连接**
3. **使用预设模板可以快速开始**
4. **根据热敏纸规格选择正确的宽度设置**

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
